# TRADPlus Test Configuration

## Quick Test Setup

### 1. Enable TRADPlus in Remote Configuration

Update your remote configuration JSON to enable TRADPlus:

```json
{
  "adKeys": {
    "isTrad": true,
    "revenueThreshold": 0.1,
    "tradInterstitialKey": "YOUR_TEST_INTERSTITIAL_ID",
    "tradInterstitialTimeKey": "YOUR_TEST_INTERSTITIAL_TIME_ID", 
    "tradOpenKey": "YOUR_TEST_APP_OPEN_ID",
    "tradNativeKey": "YOUR_TEST_NATIVE_ID",
    "tradBannerKey": "YOUR_TEST_BANNER_ID",
    "tradRewardKey": "YOUR_TEST_REWARDED_ID",
    "admobInterstitialKey": "",
    "admobInterstitialTimeKey": "",
    "admobOpenKey": "",
    "admobNative<PERSON>ey": "",
    "admobB<PERSON>rKey": "",
    "admobRewardKey": ""
  },
  "adSettings": {
    "sepInterstitial": 30,
    "rpInterstitial": 30,
    "openWait": 8,
    "interstitialWait": 8,
    "waitReward": 8,
    "switchNative": true,
    "switchBanner": true
  }
}
```

### 2. Set TRADPlus SDK Key

In `AdFusionController.kt`, set your TRADPlus SDK key:

```kotlin
const val tradplusKey = "YOUR_TRADPLUS_SDK_KEY"
```

### 3. Test Ad Unit IDs

For testing, you can use TRADPlus test ad unit IDs. Update `AdFusionConfig.kt`:

```kotlin
object TRAD {
    // Replace with your TRADPlus test ad unit IDs
    const val BANNER = "your_test_banner_id"
    const val INTERSTITIAL = "your_test_interstitial_id"
    const val INTERSTITIAL_TIME = "your_test_interstitial_time_id"
    const val REWARDED = "your_test_rewarded_id"
    const val NATIVE = "your_test_native_id"
    const val APP_OPEN = "your_test_app_open_id"
}
```

## Testing Checklist

### Before Testing
- [ ] TRADPlus SDK key is set in `AdFusionController.kt`
- [ ] Test ad unit IDs are configured in `AdFusionConfig.kt`
- [ ] Remote configuration has `isTrad: true`
- [ ] App has been rebuilt after changes

### Test Each Ad Type

#### 1. Banner Ads
- [ ] Banner ads load and display correctly
- [ ] Banner ads show in Compose UI components
- [ ] Banner ads handle lifecycle events properly

#### 2. Interstitial Ads
- [ ] Interstitial ads load successfully
- [ ] Interstitial ads display fullscreen
- [ ] Interstitial ads close properly
- [ ] New ads load after display

#### 3. Rewarded Video Ads
- [ ] Rewarded ads load successfully
- [ ] Rewarded ads play video content
- [ ] Reward callback is triggered
- [ ] Ads close properly after completion

#### 4. Native Ads
- [ ] Native ads load successfully
- [ ] Native ads render with correct layout
- [ ] All native ad elements display (icon, title, description, CTA)
- [ ] Ad choices/privacy icons are visible

#### 5. App Open/Splash Ads
- [ ] App open ads load successfully
- [ ] App open ads display on app launch
- [ ] App open ads handle container properly

## Common Test Issues

### 1. Ads Not Loading
**Possible Causes:**
- Invalid SDK key
- Incorrect ad unit IDs
- Network connectivity issues
- TRADPlus account not properly configured

**Solutions:**
- Verify SDK key and ad unit IDs
- Check TRADPlus dashboard configuration
- Test with different network connections

### 2. Native Ads Not Displaying
**Possible Causes:**
- Incorrect layout file
- Missing required view IDs
- Activity context issues

**Solutions:**
- Use `tp_native_ad_list_item.xml` layout
- Verify all required view IDs are present
- Ensure Activity context is available

### 3. Banner Ads Not Showing
**Possible Causes:**
- Container issues
- Layout problems
- Context issues

**Solutions:**
- Check container setup in AndroidView
- Verify banner is added to container
- Ensure proper context is provided

## Debug Logging

Enable debug logging to troubleshoot issues:

```kotlin
// In your Application class or before SDK initialization
TradPlusSdk.setLogLevel(TradPlusSdk.LogLevel.DEBUG)
```

Check logs for:
- SDK initialization status
- Ad loading attempts
- Error messages
- Network requests

## Production Checklist

Before going to production:
- [ ] Replace test ad unit IDs with production IDs
- [ ] Set production TRADPlus SDK key
- [ ] Disable debug logging
- [ ] Test with production configuration
- [ ] Verify revenue tracking is working
- [ ] Test GDPR/privacy compliance if applicable
