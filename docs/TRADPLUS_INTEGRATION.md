# TRADPlus Integration Guide

## Overview
This document provides instructions for configuring the TRADPlus ad mediation integration that replaces the previous MAX (AppLovin) implementation.

## Changes Made

### 1. New TRADPlus Adapter Files Created
- `TradPlusAppOpenAdapter.kt` - Splash/App Open ads
- `TradPlusInterstitialAdapter.kt` - Interstitial ads  
- `TradPlusRewardedAdapter.kt` - Rewarded video ads
- `TradPlusBannerAdapter.kt` - Banner ads
- `TradPlusNativeAdapter.kt` - Native ads

### 2. Layout File Created
- `layout_tradplus_native_ad.xml` - Native ad layout for TRADPlus

### 3. Modified Files
- `AdFusionController.kt` - Updated to use TRADPlus adapters instead of MAX adapters
- `AdEventReporter.kt` - Added TRADPlus revenue reporting method

## Configuration Required

### 1. Set TRADPlus SDK Key
Update the SDK key in `AdFusionController.kt`:
```kotlin
const val tradplusKey = "YOUR_TRADPLUS_SDK_KEY_HERE"
```

**Important**: If the SDK key is blank, TRADPlus initialization will be skipped. The system includes a 10-second timeout for initialization to prevent hanging.

### 2. Set TRADPlus Ad Unit IDs
Update the ad unit IDs in `AdFusionConfig.kt`:
```kotlin
object TRAD {
    const val BANNER = "your_tradplus_banner_id"
    const val INTERSTITIAL = "your_tradplus_interstitial_id"
    const val INTERSTITIAL_TIME = "your_tradplus_interstitial_time_id"
    const val REWARDED = "your_tradplus_rewarded_id"
    const val NATIVE = "your_tradplus_native_id"
    const val APP_OPEN = "your_tradplus_app_open_id"
}
```

### 3. Update Remote Configuration
Update your remote configuration JSON to use TRADPlus:
```json
{
  "adKeys": {
    "isTrad": true,
    "tradInterstitialKey": "your_tradplus_interstitial_id",
    "tradInterstitialTimeKey": "your_tradplus_interstitial_time_id",
    "tradOpenKey": "your_tradplus_app_open_id",
    "tradNativeKey": "your_tradplus_native_id",
    "tradBannerKey": "your_tradplus_banner_id",
    "tradRewardKey": "your_tradplus_rewarded_id"
  }
}
```

## TRADPlus SDK Dependencies

The following TRADPlus dependencies are already included in `build.gradle.kts`:
```kotlin
// TradPlus
implementation("com.tradplusad:tradplus:14.3.20.1")
implementation("com.tradplusad:tradplus-googlex:2.14.3.20.1")

// Ad Network Adapters
implementation("com.tradplusad:tradplus-facebook:1.14.3.20.1")
implementation("com.tradplusad:tradplus-pangle:19.14.3.20.1")
implementation("com.tradplusad:tradplus-fyber:24.14.3.20.1")
implementation("com.tradplusad:tradplus-mintegralx_overseas:18.14.3.20.1")
implementation("com.tradplusad:tradplus-vunglex:7.14.3.20.1")
implementation("com.tradplusad:tradplus-bigo:57.14.3.20.1")
implementation("com.tradplusad:tradplus-crosspromotion:27.14.3.20.1")
implementation("com.tradplusad:tp_exchange:40.14.3.20.1")
```

## Implementation Details

### Ad Loading and Display
All TRADPlus adapters follow the same pattern as the original MAX adapters:
- Singleton pattern for interstitial, rewarded, and app open ads
- Automatic ad reloading after display
- Proper lifecycle management
- Revenue tracking integration

### Native Ad Layout
The TRADPlus native ad layout uses the following view IDs (must match exactly):
- `tp_ad_media` - Media content view
- `tp_ad_icon` - App icon
- `tp_ad_title` - Title text
- `tp_ad_desc` - Description text
- `tp_ad_button` - Call-to-action button
- `tp_ad_choices_container` - Ad choices container for Meta/Facebook
- `tp_native_ad_choice` - Native ad choice for other networks

**Important**: The layout file `tp_native_ad_list_item.xml` follows TRADPlus standard naming and must not have its android:id values modified.

### Revenue Tracking
Revenue events are tracked through `AdEventReporter.postPaidEventByTradPlus()` method, which logs impression events for analytics.

## Testing

1. **Test Mode**: Use TRADPlus test mode for initial testing
2. **Test IDs**: Use TRADPlus test ad unit IDs during development
3. **Production**: Replace with real ad unit IDs for production

## Common Issues and Solutions

### Banner and Native Ads Not Showing
1. **Activity Context**: TRADPlus requires Activity context for proper functionality
2. **Layout IDs**: Native ads must use exact layout IDs as specified by TRADPlus
3. **Ad Unit IDs**: Ensure correct TRADPlus ad unit IDs are configured
4. **SDK Initialization**: Verify TRADPlus SDK is properly initialized with valid key

### Layout Requirements
- Use `tp_native_ad_list_item.xml` for native ads (standard TRADPlus layout name)
- Do not modify android:id values in the layout file
- Include both `tp_ad_choices_container` and `tp_native_ad_choice` for ad choices

## Notes

- TRADPlus requires Activity context for native ads and some banner ad networks
- Revenue reporting may need customization based on your TRADPlus setup
- Some TRADPlus features may differ from MAX implementation
- Ensure proper ProGuard rules are in place for TRADPlus SDK

## Additional Resources

- [TRADPlus Test Configuration Guide](./TRADPLUS_TEST_CONFIG.md)
- [TRADPlus Usage Examples](./TRADPLUS_USAGE_EXAMPLES.md)

## Support

For TRADPlus specific issues, refer to:
- [TRADPlus Android Documentation](https://docs.tradplusad.com/docs/tradplussdk_android_doc_v6/)
- [TRADPlus GitHub Demo](https://github.com/tradplus/tradplus-androidx-demo)
- TRADPlus support team

## Migration Checklist

- [ ] Set TRADPlus SDK key in `AdFusionController.kt`
- [ ] Configure TRADPlus ad unit IDs in `AdFusionConfig.kt`
- [ ] Update remote configuration to use TRADPlus (`isTrad: true`)
- [ ] Test all ad types (banner, interstitial, rewarded, native, app open)
- [ ] Verify revenue tracking is working
- [ ] Test with production ad unit IDs
- [ ] Update ProGuard rules if using code obfuscation
- [ ] Test GDPR/privacy compliance if applicable
