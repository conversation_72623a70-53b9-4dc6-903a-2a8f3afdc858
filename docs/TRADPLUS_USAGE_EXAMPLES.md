# TRADPlus Usage Examples

## Basic Usage

### 1. Initialize TRADPlus SDK

```kotlin
// In your Application class or splash activity
GlobalScope.launch {
    AdFusionController.initAdSdk(context, isTrad = true)
}
```

### 2. Load and Show Interstitial Ad

```kotlin
class MainActivity : AppCompatActivity() {
    
    private fun loadInterstitialAd() {
        val interstitialAd = AdFusionController.getInterstitialAd(this)
        
        interstitialAd.setAdListener(object : AdEventListener() {
            override fun onAdLoaded() {
                Log.d("TRADPlus", "Interstitial ad loaded")
                // Ad is ready to show
            }
            
            override fun onAdFailedToLoad() {
                Log.e("TRADPlus", "Interstitial ad failed to load")
            }
            
            override fun onAdDisplay() {
                Log.d("TRADPlus", "Interstitial ad displayed")
            }
            
            override fun onAdClosed() {
                Log.d("TRADPlus", "Interstitial ad closed")
            }
        })
        
        interstitialAd.loadAd()
    }
    
    private fun showInterstitialAd() {
        val interstitialAd = AdFusionController.getInterstitialAd(this)
        
        if (interstitialAd.isAdLoaded()) {
            interstitialAd.showAd(
                activity = this,
                onAdDismissed = {
                    Log.d("TRADPlus", "Interstitial ad dismissed")
                },
                onAdFailedToShow = {
                    Log.e("TRADPlus", "Interstitial ad failed to show")
                }
            )
        }
    }
}
```

### 3. Load and Show Rewarded Video Ad

```kotlin
private fun loadRewardedAd() {
    val rewardedAd = AdFusionController.getRewardedAd(this)
    
    rewardedAd.setAdListener(object : AdEventListener() {
        override fun onAdLoaded() {
            Log.d("TRADPlus", "Rewarded ad loaded")
        }
        
        override fun onRewarded() {
            Log.d("TRADPlus", "User earned reward")
        }
    })
    
    rewardedAd.loadAd()
}

private fun showRewardedAd() {
    val rewardedAd = AdFusionController.getRewardedAd(this)
    
    if (rewardedAd.isAdLoaded()) {
        rewardedAd.showAd(
            activity = this,
            onRewarded = { amount ->
                Log.d("TRADPlus", "User rewarded: $amount")
                // Give reward to user
            },
            onAdDismissed = {
                Log.d("TRADPlus", "Rewarded ad dismissed")
            },
            onAdFailedToShow = {
                Log.e("TRADPlus", "Rewarded ad failed to show")
            }
        )
    }
}
```

### 4. Banner Ad in Compose

```kotlin
@Composable
fun BannerAdSection() {
    val bannerAd = remember { AdFusionController.getBannerAd() }
    
    bannerAd.BannerAdView(
        adUnitId = AdFusionConfig.getBannerId(),
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
    )
}
```

### 5. Native Ad in Compose

```kotlin
@Composable
fun NativeAdSection() {
    val nativeAd = remember { AdFusionController.getNativeAd() }
    
    nativeAd.NativeAdView(
        adUnitId = AdFusionConfig.getNativeId(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        showPadding = true
    )
}
```

### 6. App Open/Splash Ad

```kotlin
class SplashActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        loadAppOpenAd()
    }
    
    private fun loadAppOpenAd() {
        val appOpenAd = AdFusionController.getAppOpenAd(this)
        
        appOpenAd.setAdListener(object : AdEventListener() {
            override fun onAdLoaded() {
                Log.d("TRADPlus", "App open ad loaded")
                showAppOpenAd()
            }
            
            override fun onAdFailedToLoad() {
                Log.e("TRADPlus", "App open ad failed to load")
                proceedToMainActivity()
            }
        })
        
        appOpenAd.loadAd()
    }
    
    private fun showAppOpenAd() {
        val appOpenAd = AdFusionController.getAppOpenAd(this)
        
        if (appOpenAd.isAdLoaded()) {
            appOpenAd.showAd(
                activity = this,
                onAdDismissed = {
                    proceedToMainActivity()
                },
                onAdFailedToShow = {
                    proceedToMainActivity()
                }
            )
        } else {
            proceedToMainActivity()
        }
    }
    
    private fun proceedToMainActivity() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
```

## Advanced Usage

### 1. Custom Ad Loading with Callbacks

```kotlin
private fun loadNativeAdWithCallback() {
    val nativeAd = AdFusionController.getNativeAd(this)
    
    nativeAd.loadAd(
        adUnitId = AdFusionConfig.getNativeId(),
        callback = { success ->
            if (success) {
                Log.d("TRADPlus", "Native ad loaded successfully")
                // Render ad in your custom container
                val container = findViewById<ViewGroup>(R.id.native_ad_container)
                nativeAd.renderAd(container, AdFusionConfig.getNativeId())
            } else {
                Log.e("TRADPlus", "Native ad failed to load")
            }
        }
    )
}
```

### 2. Check Ad Availability Before Loading

```kotlin
private fun smartAdLoading() {
    // Check if enough time has passed since last ad
    if (AdFusionConfig.allowShowAd()) {
        val interstitialAd = AdFusionController.getInterstitialAd(this)
        
        if (!interstitialAd.isAdLoaded()) {
            interstitialAd.loadAd()
        }
    }
}
```

### 3. Lifecycle Management

```kotlin
class AdActivity : AppCompatActivity() {
    
    private lateinit var nativeAd: NativeAdAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        nativeAd = AdFusionController.getNativeAd(this)
        nativeAd.loadAd(AdFusionConfig.getNativeId())
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // Clean up native ad resources
        nativeAd.destroy()
        
        // Clean up other ad types
        AdFusionController.getInterstitialAd(this).onDestroy()
        AdFusionController.getRewardedAd(this).onDestroy()
    }
}
```

## Error Handling

### 1. Handle SDK Initialization Errors

```kotlin
private fun initializeTRADPlus() {
    AdFusionController.initWithConsent(
        activity = this,
        isMax = true, // true for TRADPlus
        onComplete = {
            if (AdFusionController.sdkIsInit()) {
                Log.d("TRADPlus", "SDK initialized successfully")
                loadAds()
            } else {
                Log.e("TRADPlus", "SDK initialization failed")
                handleInitializationFailure()
            }
        }
    )
}
```

### 2. Retry Logic for Failed Ads

```kotlin
private fun loadAdWithRetry(retryCount: Int = 0) {
    val maxRetries = 3
    val interstitialAd = AdFusionController.getInterstitialAd(this)
    
    interstitialAd.setAdListener(object : AdEventListener() {
        override fun onAdLoaded() {
            Log.d("TRADPlus", "Ad loaded on attempt ${retryCount + 1}")
        }
        
        override fun onAdFailedToLoad() {
            if (retryCount < maxRetries) {
                Log.w("TRADPlus", "Ad failed, retrying... (${retryCount + 1}/$maxRetries)")
                Handler(Looper.getMainLooper()).postDelayed({
                    loadAdWithRetry(retryCount + 1)
                }, 5000) // Retry after 5 seconds
            } else {
                Log.e("TRADPlus", "Ad failed after $maxRetries attempts")
            }
        }
    })
    
    interstitialAd.loadAd()
}
```
