# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Obfuscation dictionary configuration
-classobfuscationdictionary class-dictionary.txt
-packageobfuscationdictionary package-dictionary.txt

# Keep line numbers for debugging
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep annotations
-keepattributes *Annotation*

# Keep generic signatures
-keepattributes Signature

# Keep inner classes
-keepattributes InnerClasses,EnclosingMethod

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable classes
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep constructors for classes with custom constructors
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Keep Activity classes
-keep public class * extends android.app.Activity
-keep public class * extends androidx.appcompat.app.AppCompatActivity
-keep public class * extends androidx.fragment.app.Fragment

# Keep Application class
-keep public class * extends android.app.Application

# Keep Service classes
-keep public class * extends android.app.Service

# Keep BroadcastReceiver classes
-keep public class * extends android.content.BroadcastReceiver

# Keep ContentProvider classes
-keep public class * extends android.content.ContentProvider

# Kotlin specific rules
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcher {}

# Compose specific rules
-keep class androidx.compose.** { *; }
-keep class androidx.compose.runtime.** { *; }
-keep class androidx.compose.ui.** { *; }
-dontwarn androidx.compose.**

# Keep ViewModel classes
-keep class * extends androidx.lifecycle.ViewModel {
    <init>(...);
}
-keep class * extends androidx.lifecycle.AndroidViewModel {
    <init>(...);
}

# Room database rules
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *
-dontwarn androidx.room.paging.**

# MMKV rules
-keep class com.tencent.mmkv.** { *; }
-dontwarn com.tencent.mmkv.**

# Coil image loading rules
-keep class coil.** { *; }
-dontwarn coil.**

# Lottie animation rules
-keep class com.airbnb.lottie.** { *; }
-dontwarn com.airbnb.lottie.**

# Navigation component rules
-keep class androidx.navigation.** { *; }
-dontwarn androidx.navigation.**

# Accompanist rules
-keep class com.google.accompanist.** { *; }
-dontwarn com.google.accompanist.**

# Trustlook SDK rules (if using antivirus SDK)
-keep class com.trustlook.** { *; }
-dontwarn com.trustlook.**

# Keep application specific classes
-keep class com.example.clean0522.** { *; }

# Keep data classes and models
-keep class com.example.clean0522.data.** { *; }
-keep class com.example.clean0522.model.** { *; }

# Keep database entities and DAOs
-keep class com.example.clean0522.database.** { *; }

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove custom logging utility
-assumenosideeffects class com.example.clean0522.utils.LogUtils {
    public static void d(...);
    public static void i(...);
    public static void w(...);
    public static void e(...);
    public static void v(...);
    public static void wtf(...);
    public static void performance(...);
    public static void network(...);
    public static void memory(...);
    public static void custom(...);
}

# Firebase rules
-keep class com.google.firebase.** { *; }
-keep class com.firebase.** { *; }
-keep class org.apache.** { *; }
-keepnames class com.fasterxml.jackson.** { *; }
-keepnames class javax.servlet.** { *; }
-keepnames class org.ietf.jgss.** { *; }
-dontwarn org.apache.**
-dontwarn org.w3c.dom.**
-dontwarn com.google.firebase.**
-dontwarn com.firebase.**

# Firebase Analytics
-keep class com.google.android.gms.measurement.** { *; }
-dontwarn com.google.android.gms.measurement.**

# Firebase Firestore
-keep class com.google.firebase.firestore.** { *; }
-dontwarn com.google.firebase.firestore.**

# Firebase Realtime Database
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Firebase Cloud Messaging (FCM)
-keep class com.google.firebase.messaging.** { *; }
-dontwarn com.google.firebase.messaging.**

# Firebase Auth
-keepattributes Signature
-keepattributes *Annotation*

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Obfuscation settings
-repackageclasses ''
-allowaccessmodification
-printmapping mapping.txt

# Keep crash reporting information
-keepattributes SourceFile,LineNumberTable