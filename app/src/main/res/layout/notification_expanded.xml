<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp">

    <LinearLayout
            android:id="@+id/ll_icons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

        <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

            <ImageView
                    android:id="@+id/iv_home"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@mipmap/tool_storage"
                    android:contentDescription="@string/nav_home"/>

            <TextView
                    android:id="@+id/tv_home"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav_home"
                    android:textSize="12sp"
                    android:textColor="@color/text_black"
                    android:layout_marginTop="4dp"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

            <ImageView
                    android:id="@+id/iv_antivirus"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@mipmap/tab_antivirus_s"
                    android:contentDescription="@string/nav_antivirus"/>

            <TextView
                    android:id="@+id/tv_antivirus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav_antivirus"
                    android:textSize="12sp"
                    android:textColor="@color/text_black"
                    android:layout_marginTop="4dp"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

            <ImageView
                    android:id="@+id/iv_ram"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@mipmap/tool_ram"
                    android:contentDescription="@string/nav_ram"/>

            <TextView
                    android:id="@+id/tv_ram"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav_ram"
                    android:textSize="12sp"
                    android:textColor="@color/text_black"
                    android:layout_marginTop="4dp"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

            <ImageView
                    android:id="@+id/iv_battery"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@mipmap/tool_battery"
                    android:contentDescription="@string/nav_battery"/>

            <TextView
                    android:id="@+id/tv_battery"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav_battery"
                    android:textSize="12sp"
                    android:textColor="@color/text_black"
                    android:layout_marginTop="4dp"/>
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>