<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="16dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">

    <ImageView
        android:id="@+id/noty_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentStart="true"
        android:layout_below="@id/img_close"
        android:src="@mipmap/tool_anti"/>

    <TextView
        android:id="@+id/noty_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_toEndOf="@+id/noty_icon"
        android:layout_alignTop="@+id/noty_icon"
        android:layout_toStartOf="@id/img_close"
        android:textColor="@color/text_black"
        android:textSize="16sp"
        android:textStyle="bold"
        android:ellipsize="end"
        tools:text="Worried about threats?"/>

    <TextView
        android:id="@+id/noty_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_toEndOf="@+id/noty_icon"
        android:layout_below="@+id/noty_title"
        android:textColor="@color/text_gray_70"
        android:textSize="14sp"
        android:ellipsize="end"
        tools:text="Take action to detect risks now."/>

    <Button
        android:id="@+id/btn_check"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="20dp"
        android:layout_below="@+id/noty_content"
        android:text="@string/noty_check"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textAllCaps="false"
        android:textColor="@android:color/white"
        android:background="@drawable/ad_btn_bg"/>

    <ImageView
            android:id="@+id/img_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@mipmap/icon_close"
            android:visibility="gone"
            android:layout_alignParentEnd="true"/>

</RelativeLayout>