<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/show_ad_bg"
    android:padding="5dp">

    <!-- Ad choices container for Meta/Facebook -->
    <FrameLayout
        android:id="@+id/tp_ad_choices_container"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="5dp" />

    <!-- Native ad choice for other networks -->
    <FrameLayout
        android:id="@+id/tp_native_ad_choice"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="5dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Media content section -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3">

            <!-- Media view -->
            <FrameLayout
                android:id="@+id/tp_ad_media"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:background="@color/black" />

            <!-- Ad label -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tp_ad_media"
                android:layout_margin="4dp"
                android:background="#80000000"
                android:gravity="center"
                android:paddingHorizontal="4dp"
                android:text="AD"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold" />

        </RelativeLayout>

        <!-- Content section -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_marginStart="8dp"
            android:layout_weight="2">

            <!-- App icon -->
            <ImageView
                android:id="@+id/tp_ad_icon"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:scaleType="centerCrop"
                tools:src="@mipmap/ic_launcher" />

            <!-- Title -->
            <TextView
                android:id="@+id/tp_ad_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tp_ad_icon"
                android:layout_alignBottom="@id/tp_ad_icon"
                android:layout_marginStart="4dp"
                android:layout_toEndOf="@id/tp_ad_icon"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:textColor="@color/text_black"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="TRADPlus Test Ad Title" />

            <!-- Description -->
            <TextView
                android:id="@+id/tp_ad_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@id/tp_ad_button"
                android:layout_below="@id/tp_ad_icon"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="@color/text_black"
                android:textSize="11sp"
                tools:text="This is a TRADPlus test ad description that provides more details about the advertised content." />

            <!-- Call to action button -->
            <Button
                android:id="@+id/tp_ad_button"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="4dp"
                android:background="@color/common_google_signin_btn_text_dark_pressed"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="Install" />

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>
