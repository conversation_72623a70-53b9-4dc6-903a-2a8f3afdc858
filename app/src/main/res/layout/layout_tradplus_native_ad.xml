<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/show_ad_bg"
>

    <!-- TRADPlus native ad layout based on their documentation -->
    <!-- Note: The android:id values must match TRADPlus requirements exactly -->

    <!-- Ad choices container for Meta/Facebook and other networks -->
    <FrameLayout
        android:id="@+id/tp_ad_choices_container"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="5dp" />

    <!-- Native ad choice for other networks -->
    <FrameLayout
        android:id="@+id/tp_native_ad_choice"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="5dp" />

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="5dp"
            android:orientation="horizontal">

        <RelativeLayout
                android:id="@+id/ll_media"
                android:layout_weight="3"
                android:layout_width="0dp"
                android:layout_height="wrap_content">

            <!-- Media content view for TRADPlus -->
            <FrameLayout
                    android:id="@+id/tp_ad_media"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@color/black" />

            <!-- Ad label -->
            <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/tp_ad_media"
                    android:layout_marginStart="2dp"
                    android:layout_margin="4dp"
                    android:gravity="center"
                    android:paddingHorizontal="4dp"
                    android:text="AD"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
                android:id="@+id/ll_content"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:layout_height="120dp"
                android:layout_marginStart="4dp">

            <!-- App icon -->
            <ImageView
                    android:id="@+id/tp_ad_icon"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    tools:src="@mipmap/ic_launcher" />

            <!-- Title text -->
            <TextView
                    android:id="@+id/tp_ad_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/tp_ad_icon"
                    android:layout_alignBottom="@id/tp_ad_icon"
                    android:layout_marginStart="2dp"
                    android:layout_toEndOf="@id/tp_ad_icon"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lines="2"
                    android:paddingHorizontal="4dp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textColor="@color/text_black"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    tools:text="TRADPlus Test Ad" />

            <!-- Body text -->
            <TextView
                    android:id="@+id/tp_ad_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/tp_ad_button"
                    android:layout_below="@id/tp_ad_icon"
                    android:ellipsize="end"
                    android:lineSpacingExtra="2sp"
                    android:paddingEnd="2dp"
                    android:lineHeight="13sp"
                    android:textColor="@color/text_black"
                    android:textSize="11sp"
                    tools:text="This is a TRADPlus Test Ad description text that shows the ad content" />

            <!-- Call to action button -->
            <Button
                    android:id="@+id/tp_ad_button"
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:layout_marginBottom="4dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerVertical="true"
                    android:layout_marginHorizontal="4dp"
                    android:background="@color/common_google_signin_btn_text_dark_pressed"
                    android:textColor="@color/text_black"
                    android:textSize="12sp"
                    tools:text="Install" />

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>
