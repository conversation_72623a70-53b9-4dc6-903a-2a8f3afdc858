<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:padding="8dp">

    <ImageView
        android:id="@+id/noty_icon"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="8dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:src="@mipmap/tool_anti"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@+id/noty_icon"
        android:layout_toStartOf="@+id/btn_check"
        android:layout_centerVertical="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/noty_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_black"
            android:textSize="14sp"
            android:textStyle="bold"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="Worried about threats?"/>

        <TextView
            android:id="@+id/noty_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_gray_70"
            android:textSize="12sp"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="Click to view detailed memory usage."/>

    </LinearLayout>

    <Button
        android:id="@+id/btn_check"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:text="@string/noty_check"
        android:textSize="16sp"
        android:textAllCaps="false"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:background="@drawable/btn_click"/>

</RelativeLayout>