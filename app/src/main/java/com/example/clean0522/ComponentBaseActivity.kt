package com.example.clean0522

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.notify.BarServiceManager
import com.example.clean0522.ui.components.NotificationPermissionDialog
import com.example.clean0522.ui.components.RewardedAdDialog
import com.example.clean0522.ui.components.ScoreDialog
import com.example.clean0522.ui.components.StoragePermissionDialog
import com.example.clean0522.ui.components.checkAllowShowScoreDialog
import com.example.clean0522.ui.navigation.startFileBrowserActivityWithPermission
import com.example.clean0522.ui.navigation.startFileUtilityActivityWithPermission
import com.example.clean0522.utils.PermissionUtils
import com.example.clean0522.utils.startCountdown
import com.example.clean0522.utils.AnimationEvent
import com.example.clean0522.utils.AnimationEventManager
import com.example.clean0522.utils.logD
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.interfaces.RewardedAdAdapter
import kotlinx.coroutines.launch

abstract class ComponentBaseActivity: ComponentActivity() {

    private var showStoragePermissionDialog by mutableStateOf(false)
    private var storagePendingAction: (() -> Unit)? = null

    private var showNotificationPermissionDialog by mutableStateOf(false)
    private var notificationPendingAction: (() -> Unit)? = null

    private var showRewardedAdDialog by mutableStateOf(false)
    private var rewardedAdPendingAction: (() -> Unit)? = null

    private var intersAdInstance: InterstitialAdAdapter? = null
    private var rewardedAdInstance: RewardedAdAdapter? = null
    private var adIsLoaded = false
    private var adIsShowed = false

    private var rewardGet = false
    private var rewardLoaded = false
    private var rewardShowed = false

    private val storagePermissionSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        if (PermissionUtils.hasStoragePermission(this)) {
            storagePendingAction?.invoke()
        }
        storagePendingAction = null
    }

    private val notificationPermissionSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { _ ->
        if (PermissionUtils.hasNotificationPermission(this)) {
            notificationPendingAction?.invoke()
        }
        notificationPendingAction = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            var showLoading by remember { mutableStateOf(false) }
            var showScoreDialog by remember { mutableStateOf(false) }

            intersAdInstance = AdFusionController.getInterstitialAd(this).apply {
                setAdListener(object : AdEventListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        adIsLoaded = true
                        adIsShowed = false
                    }

                    override fun onAdDisplay() {
                        super.onAdDisplay()
                        adIsShowed = true
                    }
                })
                loadAd()
            }

            // Initialize rewarded ad
            rewardedAdInstance = AdFusionController.getRewardedAd(this).apply {
                setAdListener(object : AdEventListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        rewardGet = false
                        rewardLoaded = true
                        rewardShowed = false
                    }

                    override fun onAdDisplay() {
                        super.onAdDisplay()
                        rewardShowed = true
                    }
                })
                loadAd()
            }

            setHomePage()

            if (showStoragePermissionDialog) {
                StoragePermissionDialog(
                    onDismiss = {
                        showStoragePermissionDialog = false
                        storagePendingAction = null
                    },
                    onAllow = {
                        showStoragePermissionDialog = false
                        val intent = PermissionUtils.getStoragePermissionIntent(this@ComponentBaseActivity)
                        storagePermissionSettingsLauncher.launch(intent)
                    }
                )
            }

            if (showNotificationPermissionDialog) {
                NotificationPermissionDialog(
                    onDismiss = {
                        showNotificationPermissionDialog = false
                        notificationPendingAction = null
                    },
                    onAllow = {
                        showNotificationPermissionDialog = false
                        if (NotificationPreferencesManager.getInstance().getNotificationCount() == 1){
                            val intent =  PermissionUtils.getNotificationPermissionIntent(this@ComponentBaseActivity)
                            notificationPermissionSettingsLauncher.launch(intent)
                        }else{
                            PermissionUtils.requestNotificationPermission(this)
                        }
                    }
                )
            }

            if (showRewardedAdDialog) {
                RewardedAdDialog(
                    onDismiss = {
                        showRewardedAdDialog = false
                        rewardedAdPendingAction = null
                    },
                    onWatchAd = {
                        showRewardedAdDialog = false
                        showLoading = true
                        startCountdown(
                            durationSeconds = AdFusionConfig.rewardLoadTime,
                            onTick = {
                                if (rewardLoaded && !rewardShowed && it > 2){
                                    rewardedAdInstance?.showAd(
                                        activity = this@ComponentBaseActivity,
                                        onRewarded = { amount ->
                                            rewardGet = true
                                        },
                                        onAdDismissed = {
                                            if (rewardGet){
                                                rewardedAdPendingAction?.invoke()
                                                rewardedAdPendingAction = null
                                            }
                                        },
                                        onAdFailedToShow = {
                                        }
                                    )
                                }
                            },
                            onFinish = {
                                if (!rewardShowed){
                                    rewardedAdPendingAction = null
                                }
                                showLoading = false
                            }
                        )
                    }
                )
            }

            if (showScoreDialog){
                ScoreDialog{
                    showScoreDialog = false
                }
            }

            if (showLoading){
                Box(modifier = Modifier.fillMaxSize()
                    .background(color = Color(0x80000000)),
                    contentAlignment = Alignment.Center){
                    CircularProgressIndicator(
                        modifier = Modifier.size(48.dp),
                        color = Color.White,
                        strokeWidth = 4.dp
                    )
                }
            }
            val lifecycleOwner = LocalLifecycleOwner.current

            LaunchedEffect(Unit) {
                AnimationEventManager.animationEventFlow.flowWithLifecycle(lifecycleOwner.lifecycle,
                    Lifecycle.State.STARTED).collect { event ->
                    if (this@ComponentBaseActivity is MainActivity){
                        when (event) {
                            is AnimationEvent.Start -> {
                                if (checkAllowShowScoreDialog()){
                                    showScoreDialog = true
                                }else if (!showStoragePermissionDialog && !showNotificationPermissionDialog && ComPreferencesManager.instance.isShowGuide()){
                                    PermissionUtils.checkRequestNotificationPermission(this@ComponentBaseActivity){
                                        showNotificationPermissionDialog = true
                                    }
                                }else if(AdFusionConfig.allowShowAd()){
                                    showLoading = true
                                    startCountdown(
                                        durationSeconds = AdFusionConfig.intersLoadTime,
                                        onTick = {
                                            if (adIsLoaded && !adIsShowed && it > 2) {
                                                intersAdInstance?.showAd(
                                                    this@ComponentBaseActivity,
                                                    onAdDismissed = {
                                                        launch {
                                                            if (adIsShowed){
                                                                AnimationEventManager.onAnimationDismissed()
                                                            }
                                                        }
                                                    })
                                            }
                                        },
                                        onFinish = {
                                            launch {
                                                AnimationEventManager.onAnimationDismissed()
                                            }
                                        }
                                    )
                                }
                            }
                            is AnimationEvent.Dismissed -> {
                                showLoading = false
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (this !is MainActivity){
            if (!showStoragePermissionDialog && !showNotificationPermissionDialog && ComPreferencesManager.instance.isShowGuide()){
                PermissionUtils.checkRequestNotificationPermission(this){
                    showNotificationPermissionDialog = true
                }
            }
        }
    }

    @Composable
    abstract fun setHomePage()

    /**
     * Check storage permission and execute action if granted, or show permission dialog
     * @param action The action to execute if permission is granted
     */
    fun checkStoragePermissionAndExecute(action: () -> Unit) {
        if (PermissionUtils.hasStoragePermission(this)) {
            action()
        } else {
            storagePendingAction = action
            showStoragePermissionDialog = true
        }
    }

    /**
     * Check notification permission and execute action if granted, or show permission dialog
     * @param action The action to execute if permission is granted
     */
    fun checkNotificationPermissionAndExecute(action: () -> Unit) {
        if (PermissionUtils.hasNotificationPermission(this)) {
            action()
        } else {
            notificationPendingAction = action
            showNotificationPermissionDialog = true
        }
    }

    /**
     * Show rewarded ad dialog and execute action after user watches the ad
     * @param action The action to execute after user watches the rewarded ad
     */
    fun showRewardedAdAndExecute(action: () -> Unit) {
        rewardedAdPendingAction = action
        showRewardedAdDialog = true
    }

    /**
     * Navigate to different features based on feature ID
     */
    fun navigateToFeature(featureId: String) {
        try {
            when (featureId) {
                "device_storage" -> {
                    startFileBrowserActivityWithPermission(this, FileBrowserActivity.TYPE_STORAGE)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "security_scan" -> {
                    val intent = Intent(this, MainActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
                        putExtra("navigate_to", "security_scan")
                    }
                    startActivity(intent)
                    if (this !is MainActivity){
                        finish()
                    }
                }
                "recent_files" -> {
                    startFileUtilityActivityWithPermission(this, FileUtilityActivity.TYPE_RECENT_FILES)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "large_files" -> {
                    startFileUtilityActivityWithPermission(this, FileUtilityActivity.TYPE_LARGE_FILES)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "duplicate_files" -> {
                    startFileUtilityActivityWithPermission(this, FileUtilityActivity.TYPE_DUPLICATE_FILES)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "redundant_files" -> {
                    startFileUtilityActivityWithPermission(this, FileUtilityActivity.TYPE_REDUNDANT_FILES)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "ram_usage" -> {
                    val intent = Intent(this, SystemInfoActivity::class.java).apply {
                        putExtra("info_type", "ram_usage")
                    }
                    startActivity(intent)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "battery_info" -> {
                    val intent = Intent(this, SystemInfoActivity::class.java).apply {
                        putExtra("info_type", "battery_info")
                    }
                    startActivity(intent)
                    if (this !is MainActivity){
                        super.finish()
                    }
                }
                "app_manager" -> {
                    val intent = Intent(this, AppInfoActivity::class.java).apply {
                        putExtra(AppInfoActivity.EXTRA_APP_TYPE, AppInfoActivity.TYPE_APP_MANAGER)
                    }
                    startActivity(intent)
                }
                else -> {
                    val intent = Intent(this, MainActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    }
                    startActivity(intent)
                    super.finish()
                }
            }

        } catch (e: Exception) {

        }
    }

    override fun finish() {
        super.finish()
        Handler(Looper.getMainLooper()).postDelayed({
            AnimationEventManager.triggerAnimation()
        }, 400)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        try {
            if (permissions[0] == Manifest.permission.POST_NOTIFICATIONS) {
                if (PermissionUtils.hasNotificationPermission(this)) {
                    BarServiceManager.startService(this)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {
            if (requestCode == PermissionUtils.REQUEST_CODE_NOTIFICATION) {
                if (PermissionUtils.hasNotificationPermission(this)) {
                    BarServiceManager.startService(this)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
