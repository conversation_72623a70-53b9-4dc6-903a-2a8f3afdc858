package com.example.clean0522.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.core.content.ContextCompat
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.notify.NotificationShowManage
import com.ext.firbase.FBAppUtil
import com.ext.remoteset.AnalyticsManager
import com.ext.remoteset.ConfigManager

class TickRingReceiver: BroadcastReceiver() {
    override fun onReceive(p0: Context?, p1: Intent?) {
        FBAppUtil.fetchRemoteConfig()
        ConfigManager.newConfigFetchRequest()
        AnalyticsManager.checkAndRequestErrorPost()
        p0?.let { context ->
            if (NotificationPreferencesManager.getInstance().getTopicFlag()){
                NotificationShowManage.showTipNotificationByLoop(context)
            }
        }
    }

    fun registerReceiver(context:Context){
        val filter = IntentFilter().also { it.addAction(Intent.ACTION_TIME_TICK) }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            context.registerReceiver(this,filter, Context.RECEIVER_EXPORTED)
        }else{
            ContextCompat.registerReceiver(context, this, filter, ContextCompat.RECEIVER_NOT_EXPORTED)
        }
    }
}