package com.example.clean0522.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.core.content.ContextCompat
import com.example.clean0522.notify.TipsShowManager

class TipShowReceiver: BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action != null){
            when (intent.action) {
                TipsShowManager.Companion.NOTIFICATION_CANCEL_MSG -> {
                    TipsShowManager.Companion.cancelNotification()
                }
                Intent.ACTION_SCREEN_OFF -> {
                    TipsShowManager.Companion.closeHandel()
                }
            }
        }
    }

    fun registerReceiver(context:Context){
        val filter = IntentFilter().also {
            it.addAction(TipsShowManager.Companion.NOTIFICATION_CANCEL_MSG)
            it.addAction(Intent.ACTION_SCREEN_OFF)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU){
            context.registerReceiver(this,filter, Context.RECEIVER_EXPORTED)
        }else{
            ContextCompat.registerReceiver(context, this, filter, ContextCompat.RECEIVER_NOT_EXPORTED)
        }
    }
}