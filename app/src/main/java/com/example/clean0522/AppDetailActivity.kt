package com.example.clean0522

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.AppDetailUtils
import com.example.clean0522.viewmodel.AppDetailViewModel
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

/**
 * Activity for displaying detailed app information
 */
class AppDetailActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_PACKAGE_NAME = "package_name"
        const val EXTRA_APP_NAME = "app_name"

        fun createIntent(context: Context, packageName: String, appName: String): Intent {
            return Intent(context, AppDetailActivity::class.java).apply {
                putExtra(EXTRA_PACKAGE_NAME, packageName)
                putExtra(EXTRA_APP_NAME, appName)
            }
        }
    }

    @Composable
    override fun setHomePage() {
        val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME) ?: ""
        val appName = intent.getStringExtra(EXTRA_APP_NAME) ?: ""

        Clean0522Theme {
            AppDetailScreen(
                packageName = packageName,
                appName = appName,
                onBackClick = { finish() }
            )
        }
    }
}

/**
 * App detail screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDetailScreen(
    packageName: String,
    appName: String,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val viewModel: AppDetailViewModel = viewModel()
    val appDetailInfo by viewModel.appDetailInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    var selectedTabIndex by remember { mutableStateOf(0) }

    // Load app detail when first composed
    LaunchedEffect(packageName) {
        if (packageName.isNotEmpty()) {
            viewModel.loadAppDetail(context, packageName)
        }
    }

    Scaffold(
        modifier = Modifier.navigationBarsPadding(),
        containerColor = colorResource(R.color.bg_color),
        topBar = {
            Column {
                TopNavBar(
                    title = appName,
                    showBackButton = true,
                    backButtonAction = onBackClick
                )
                if (AdFusionConfig.openBanner()){
                    AdFusionController.getBannerAd().BannerAdView()
                }
            }
        },
        bottomBar = {
            AdFusionController.getNativeAd().NativeAdView()
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (error != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = error!!,
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                viewModel.clearError()
                                viewModel.loadAppDetail(context, packageName)
                            }
                        ) {
                            Text(stringResource(R.string.retry))
                        }
                    }
                }
            } else if (appDetailInfo != null) {
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        AppDetailTabChip(
                            text = stringResource(R.string.tab_general),
                            isSelected = selectedTabIndex == 0,
                            onClick = { selectedTabIndex = 0 },
                            modifier = Modifier.weight(1f)
                        )
                        AppDetailTabChip(
                            text = stringResource(R.string.tab_permission),
                            isSelected = selectedTabIndex == 1,
                            onClick = { selectedTabIndex = 1 },
                            modifier = Modifier.weight(1f)
                        )
                        AppDetailTabChip(
                            text = stringResource(R.string.tab_certificates),
                            isSelected = selectedTabIndex == 2,
                            onClick = { selectedTabIndex = 2 },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    Spacer(modifier = Modifier.height(1.dp)
                        .fillMaxWidth()
                        .background(colorResource(R.color.text_gray)))
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Content based on selected tab
                when (selectedTabIndex) {
                    0 -> GeneralTabContent(appDetailInfo!!)
                    1 -> PermissionTabContent(appDetailInfo!!.permissions)
                    2 -> CertificatesTabContent(appDetailInfo!!.certificates)
                }
            }
        }
    }
}

/**
 * General tab content showing app basic information
 */
@Composable
fun GeneralTabContent(appDetailInfo: AppDetailUtils.AppDetailInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(bottom = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            AppInfoItem(
                label = stringResource(R.string.apk_size),
                value = AppDetailUtils.formatFileSize(appDetailInfo.apkSize)
            )
            AppInfoItem(
                label = stringResource(R.string.application_name),
                value = appDetailInfo.appName
            )
            AppInfoItem(
                label = stringResource(R.string.min_sdk),
                value = appDetailInfo.minSdkVersion.toString()
            )

            AppInfoItem(
                label = stringResource(R.string.min_sdk_version),
                value = appDetailInfo.minSdkVersion.toString()
            )

            AppInfoItem(
                label = stringResource(R.string.package_name),
                value = appDetailInfo.packageName
            )

            AppInfoItem(
                label = stringResource(R.string.process_name),
                value = appDetailInfo.processName
            )

            AppInfoItem(
                label = stringResource(R.string.system_application),
                value = stringResource(if (appDetailInfo.isSystemApp) R.string.yes else R.string.no)
            )

            AppInfoItem(
                label = stringResource(R.string.target_sdk),
                value = appDetailInfo.targetSdkVersion.toString()
            )

            AppInfoItem(
                label = stringResource(R.string.target_sdk_version),
                value = appDetailInfo.targetSdkVersion.toString()
            )

            AppInfoItem(
                label = stringResource(R.string.version_code),
                value = appDetailInfo.versionCode.toString()
            )

            AppInfoItem(
                label = stringResource(R.string.version_name),
                value = appDetailInfo.versionName
            )
        }
    }
}

/**
 * Individual app info item
 */
@Composable
fun AppInfoItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_gray_70),
            modifier = Modifier.weight(1f)
        )

        Text(
            text = value,
            fontSize = 13.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            textAlign = TextAlign.End,
            modifier = Modifier.padding(start = 2.dp)
                .weight(1.3f)
        )
    }
}

/**
 * Permission tab content showing app permissions
 */
@Composable
fun PermissionTabContent(permissions: List<AppDetailUtils.AppPermissionInfo>) {
    if (permissions.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_permissions_found),
                fontSize = 16.sp,
                color = colorResource(R.color.text_black)
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(permissions) { permission ->
                PermissionItem(permission = permission)
            }
        }
    }
}

/**
 * Individual permission item
 */
@Composable
fun PermissionItem(permission: AppDetailUtils.AppPermissionInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Permission display name (uppercase, formatted)
            Text(
                text = permission.name.substringAfterLast(".").replace("_", " "),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource( R.color.text_black)
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Protection level
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.protection_level),
                    fontSize = 13.sp,
                    color = colorResource(R.color.text_gray_70),
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = permission.protectionLevel,
                    fontSize = 13.sp,
                    lineHeight = 16.sp,
                    color = colorResource(R.color.text_black),
                    textAlign = TextAlign.End,
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Constant value (full permission name)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.constant_value),
                    fontSize = 13.sp,
                    color = colorResource(R.color.text_gray_70),
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = permission.name,
                    fontSize = 13.sp,
                    lineHeight = 16.sp,
                    color = colorResource(R.color.text_black),
                    textAlign = TextAlign.End,
                    modifier = Modifier.weight(1f),
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Grant status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.grant_status),
                    fontSize = 13.sp,
                    color = colorResource(R.color.text_gray_70),
                    modifier = Modifier.weight(1f)
                )
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = if (permission.isGranted)
                            MaterialTheme.colorScheme.primaryContainer
                        else
                            MaterialTheme.colorScheme.errorContainer
                    ),
                    shape = RoundedCornerShape(4.dp),
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Text(
                        text = stringResource(if (permission.isGranted) R.string.permission_granted else R.string.permission_denied),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.text_black),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Description
            if (permission.description != permission.name && permission.description.isNotBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = permission.description,
                    fontSize = 13.sp,
                    color = colorResource(R.color.text_black),
                    lineHeight = 16.sp
                )
            }
        }
    }
}

/**
 * Certificates tab content showing app certificates
 */
@Composable
fun CertificatesTabContent(certificates: List<AppDetailUtils.CertificateInfo>) {
    if (certificates.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_certificates_found),
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 8.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(certificates) { certificate ->
                CertificateItem(certificate = certificate)
            }
        }
    }
}

/**
 * Individual certificate item
 */
@Composable
fun CertificateItem(certificate: AppDetailUtils.CertificateInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Sign algorithm
            CertificateInfoRow(
                label = stringResource(R.string.sign_algorithm),
                value = certificate.signatureAlgorithm
            )

            // Valid from
            CertificateInfoRow(
                label = stringResource(R.string.valid_from),
                value = certificate.validFrom
            )

            // Valid to
            CertificateInfoRow(
                label = stringResource(R.string.valid_to),
                value = certificate.validTo
            )

            // Public key MD5
            CertificateInfoRow(
                label = stringResource(R.string.public_key_md5),
                value = certificate.publicKeyMD5
            )

            // Certificate MD5
            CertificateInfoRow(
                label = stringResource(R.string.certificate_md5),
                value = certificate.certificateMD5
            )

            // Serial number
            CertificateInfoRow(
                label = stringResource(R.string.serial_number),
                value = certificate.serialNumber
            )

            // Issuer name
            CertificateInfoRow(
                label = stringResource(R.string.issuer_name),
                value = certificate.issuerName
            )

            // Issuer organization
            CertificateInfoRow(
                label = stringResource(R.string.issuer_organization),
                value = certificate.issuerOrganization
            )

            // Issuer country
            CertificateInfoRow(
                label = stringResource(R.string.issuer_country),
                value = certificate.issuerCountry
            )

            // Subject name
            CertificateInfoRow(
                label = stringResource(R.string.subject_name),
                value = certificate.subjectName
            )

            // Subject organization
            CertificateInfoRow(
                label = stringResource(R.string.subject_organization),
                value = certificate.subjectOrganization
            )

            // Subject country
            CertificateInfoRow(
                label = stringResource(R.string.subject_country),
                value = certificate.subjectCountry
            )
        }
    }
}

/**
 * Certificate information row
 */
@Composable
fun CertificateInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_gray_70),
            modifier = Modifier.weight(1f)
        )

        Text(
            text = value,
            fontSize = 13.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_black),
            textAlign = TextAlign.End,
            modifier = Modifier.padding(start = 2.dp)
                .weight(1.3f)
        )
    }
}

/**
 * App detail tab chip component (same style as AppInfoActivity)
 */
@Composable
fun AppDetailTabChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                if (isSelected) {
                    colorResource(R.color.text_blue)
                } else {
                    Color.Transparent
                }
                , shape = RoundedCornerShape(topStart = 7.dp, topEnd = 7.dp)
            )
            .clickable { onClick() }
            .padding(vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontWeight = FontWeight.SemiBold,
            color = if (isSelected) {
                Color.White
            } else {
                Color(0xFFA3A3A3)
            },
            fontSize = 12.sp
        )
    }
}
