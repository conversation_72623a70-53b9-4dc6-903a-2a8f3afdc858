package com.example.clean0522.utils

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.example.clean0522.AppInfoActivity
import com.example.clean0522.FileBrowserActivity
import com.example.clean0522.FileUtilityActivity
import com.example.clean0522.SystemInfoActivity
import com.example.clean0522.R
/**
 * Lottie animation utility for loading different animations based on type
 * Supports all available animations in assets folder
 */
object LottieAnimationUtils {

    /**
     * Animation types corresponding to JSON files in assets
     */
    enum class AnimationType(val fileName: String, val displayName: String) {
        APP_MANAGER("AppManager.json", AppInfoActivity.TYPE_APP_MANAGER),
        APP_PROCESS("AppProcess.json", SystemInfoActivity.TYPE_APP_PROCESS),
        BATTERY("Battery.json", SystemInfoActivity.TYPE_BATTERY_INFO),
        CPU("Cpu.json", SystemInfoActivity.TYPE_CPU_MONITOR),
        DUPLICATE_FILES("DuplicateFiles.json", FileUtilityActivity.TYPE_DUPLICATE_FILES),
        LARGE_FILES("LargeFiles.json", FileUtilityActivity.TYPE_LARGE_FILES),
        NORMAL_LOAD("normal_load.json", "load"),
        RAM("RAM.json", SystemInfoActivity.TYPE_RAM_USAGE),
        RECENT_FILES("recentFiles.json", FileUtilityActivity.TYPE_RECENT_FILES),
        REDUNDANT_FILES("RedundantFIles.json", FileUtilityActivity.TYPE_REDUNDANT_FILES),
        SEARCH("search.json", "search"),
        STORAGE("storage.json", FileBrowserActivity.TYPE_STORAGE),
        TRASH("trash.json", "trash"),
        VIRUS("virus.json", "virus_scan");

        companion object {
            fun fromString(type: String): AnimationType {
                return values().find {
                    it.name.equals(type, ignoreCase = true) ||
                            it.displayName.equals(type, ignoreCase = true)
                } ?: SEARCH
            }
        }
    }

    /**
     * Get Lottie composition spec for given animation type
     */
    fun getLottieCompositionSpec(animationType: AnimationType): LottieCompositionSpec {
        return LottieCompositionSpec.Asset(animationType.fileName)
    }

    /**
     * Get Lottie composition spec from type string
     */
    fun getLottieCompositionSpec(type: String): LottieCompositionSpec {
        val animationType = AnimationType.fromString(type)
        return getLottieCompositionSpec(animationType)
    }
}

/**
 * Simple Lottie animation composable
 */
@Composable
fun LottieAnimationView(
    animationType: LottieAnimationUtils.AnimationType,
    modifier: Modifier = Modifier,
    size: Dp = 400.dp,
    iterations: Int = LottieConstants.IterateForever,
    isPlaying: Boolean = true,
    restartOnPlay: Boolean = true,
    speed: Float = 1f
) {
    val composition by rememberLottieComposition(
        LottieAnimationUtils.getLottieCompositionSpec(animationType)
    )

    val progress by animateLottieCompositionAsState(
        composition = composition,
        isPlaying = isPlaying,
        iterations = iterations,
        restartOnPlay = restartOnPlay,
        speed = speed
    )

    Box(modifier = Modifier.fillMaxSize()
        .background(colorResource(R.color.bg_color)),
        contentAlignment = Alignment.Center
    ){
        LottieAnimation(
            composition = composition,
            progress = { progress },
            modifier = modifier.width(size)
        )
    }
}

/**
 * Lottie animation from type string
 */
@Composable
fun LottieAnimationView(
    type: String,
    modifier: Modifier = Modifier,
    size: Dp = 400.dp,
    iterations: Int = LottieConstants.IterateForever,
    isPlaying: Boolean = true,
    restartOnPlay: Boolean = true,
    speed: Float = 1f
) {
    val animationType = LottieAnimationUtils.AnimationType.fromString(type)
    LottieAnimationView(
        animationType = animationType,
        modifier = modifier,
        size = size,
        iterations = iterations,
        isPlaying = isPlaying,
        restartOnPlay = restartOnPlay,
        speed = speed
    )
}

/**
 * Loading screen with Lottie animation and text
 */
@Composable
fun LottieLoadingScreen(
    animationType: LottieAnimationUtils.AnimationType,
    loadingText: String = animationType.displayName,
    modifier: Modifier = Modifier,
    animationSize: Dp = 200.dp,
    textSize: TextUnit = 16.sp,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    isPlaying: Boolean = true,
    backgroundColor: Color = MaterialTheme.colorScheme.surface
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        LottieAnimationView(
            animationType = animationType,
            size = animationSize,
            isPlaying = isPlaying
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = loadingText,
            fontSize = textSize,
            fontWeight = FontWeight.Medium,
            color = textColor,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * Loading screen from type string
 */
@Composable
fun LottieLoadingScreen(
    type: String,
    loadingText: String? = null,
    modifier: Modifier = Modifier,
    animationSize: Dp = 200.dp,
    textSize: TextUnit = 16.sp,
    textColor: Color = MaterialTheme.colorScheme.onSurface,
    isPlaying: Boolean = true
) {
    val animationType = LottieAnimationUtils.AnimationType.fromString(type)
    val displayText = loadingText ?: animationType.displayName

    LottieLoadingScreen(
        animationType = animationType,
        loadingText = displayText,
        modifier = modifier,
        animationSize = animationSize,
        textSize = textSize,
        textColor = textColor,
        isPlaying = isPlaying
    )
}

/**
 * Compact Lottie animation with optional text
 */
@Composable
fun CompactLottieAnimation(
    type: String,
    text: String? = null,
    modifier: Modifier = Modifier,
    size: Dp = 80.dp,
    textSize: TextUnit = 12.sp,
    spacing: Dp = 8.dp,
    isPlaying: Boolean = true,
    arrangement: Arrangement.Vertical = Arrangement.Center
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = arrangement
    ) {
        LottieAnimationView(
            type = type,
            size = size,
            isPlaying = isPlaying
        )

        text?.let {
            Spacer(modifier = Modifier.height(spacing))
            Text(
                text = it,
                fontSize = textSize,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * Extension functions for easy usage
 */

/**
 * Show Lottie animation with automatic type detection
 */
@Composable
fun Any.showLottieAnimation(
    type: String,
    modifier: Modifier = Modifier,
    size: Dp = 200.dp,
    isPlaying: Boolean = true
) {
    LottieAnimationView(
        type = type,
        modifier = modifier,
        size = size,
        isPlaying = isPlaying
    )
}

/**
 * Show loading screen with Lottie animation
 */
@Composable
fun Any.showLottieLoadingScreen(
    type: String,
    loadingText: String? = null,
    modifier: Modifier = Modifier,
    animationSize: Dp = 200.dp,
    isPlaying: Boolean = true
) {
    LottieLoadingScreen(
        type = type,
        loadingText = loadingText,
        modifier = modifier,
        animationSize = animationSize,
        isPlaying = isPlaying
    )
}

/**
 * Available animation types as string constants for easy reference
 */
object LottieAnimationTypes {
    const val APP_MANAGER = "APP_MANAGER"
    const val APP_PROCESS = "APP_PROCESS"
    const val BATTERY = "BATTERY"
    const val CPU = "CPU"
    const val DUPLICATE_FILES = "DUPLICATE_FILES"
    const val LARGE_FILES = "LARGE_FILES"
    const val NORMAL_LOAD = "NORMAL_LOAD"
    const val RAM = "RAM"
    const val RECENT_FILES = "RECENT_FILES"
    const val REDUNDANT_FILES = "REDUNDANT_FILES"
    const val SEARCH = "SEARCH"
    const val STORAGE = "STORAGE"
    const val TRASH = "TRASH"
    const val VIRUS = "VIRUS"
}