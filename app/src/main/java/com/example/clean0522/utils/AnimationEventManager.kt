package com.example.clean0522.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

object AnimationEventManager {

    private val _animationEventFlow = MutableSharedFlow<AnimationEvent>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val animationEventFlow: SharedFlow<AnimationEvent> = _animationEventFlow.asSharedFlow()


    suspend fun startAnimation() {
        _animationEventFlow.emit(AnimationEvent.Start)
    }

    suspend fun onAnimationDismissed() {
        _animationEventFlow.emit(AnimationEvent.Dismissed)
    }

    fun triggerAnimation() {
        CoroutineScope(Dispatchers.Default).launch {
            try {
                startAnimation()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}

sealed class AnimationEvent {
    object Start : AnimationEvent()
    object Dismissed : AnimationEvent()
}
