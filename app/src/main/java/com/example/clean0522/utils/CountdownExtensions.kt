package com.example.clean0522.utils

import android.os.CountDownTimer
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

/**
 * Countdown utility with per-second callbacks and completion callback
 * Supports both coroutine-based and traditional callback-based countdown
 */

/**
 * Active countdown timers storage to allow cancellation
 */
private val activeCountdowns = ConcurrentHashMap<String, CountDownTimer>()

/**
 * Generate unique key for countdown identification
 */
private fun Any.getCountdownKey(): String = "${this::class.java.simpleName}_${this.hashCode()}"

/**
 * Start a countdown timer with callbacks for each second and completion
 *
 * @param durationSeconds Total countdown duration in seconds
 * @param onTick Callback invoked every second with remaining seconds
 * @param onFinish Callback invoked when countdown completes
 * @param tag Optional tag for identification (defaults to class name)
 */
fun Any.startCountdown(
    durationSeconds: Long = 8,
    onTick: (remainingSeconds: Long) -> Unit,
    onFinish: () -> Unit,
    tag: String = this::class.java.simpleName
) {
    val key = getCountdownKey()

    // Cancel any existing countdown for this object
    stopCountdown()

    val timer = object : CountDownTimer(durationSeconds * 1000, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            val secondsRemaining = (millisUntilFinished / 1000).coerceAtLeast(0)
            LogUtils.d(tag, "Countdown tick: ${durationSeconds - secondsRemaining}s remaining")
            onTick(durationSeconds - secondsRemaining)
        }

        override fun onFinish() {
            LogUtils.d(tag, "Countdown finished")
            activeCountdowns.remove(key)
            onFinish()
        }
    }

    activeCountdowns[key] = timer
    timer.start()
    LogUtils.d(tag, "Started countdown: ${durationSeconds}s")
}

/**
 * Start a coroutine-based countdown timer
 *
 * @param durationSeconds Total countdown duration in seconds
 * @param scope CoroutineScope for the countdown
 * @param onTick Suspend callback invoked every second with remaining seconds
 * @param onFinish Suspend callback invoked when countdown completes
 * @param tag Optional tag for identification
 */
fun Any.startCountdownCoroutine(
    durationSeconds: Long,
    scope: CoroutineScope,
    onTick: suspend (remainingSeconds: Long) -> Unit,
    onFinish: suspend () -> Unit,
    tag: String = this::class.java.simpleName
): Job {
    LogUtils.d(tag, "Started coroutine countdown: ${durationSeconds}s")

    return scope.launch {
        repeat(durationSeconds.toInt()) { index ->
            val remaining = durationSeconds - index
            LogUtils.d(tag, "Coroutine countdown tick: ${remaining}s remaining")
            onTick(remaining)
            delay(1000)
        }
        LogUtils.d(tag, "Coroutine countdown finished")
        onFinish()
    }
}

/**
 * Stop any active countdown for this object
 */
fun Any.stopCountdown(tag: String = this::class.java.simpleName) {
    val key = getCountdownKey()
    activeCountdowns[key]?.let { timer ->
        timer.cancel()
        activeCountdowns.remove(key)
        LogUtils.d(tag, "Countdown stopped")
    }
}

/**
 * Check if this object has an active countdown
 */
fun Any.hasActiveCountdown(): Boolean {
    val key = getCountdownKey()
    return activeCountdowns.containsKey(key)
}

/**
 * Convenient countdown with formatted time display
 *
 * @param durationSeconds Total countdown duration in seconds
 * @param onTickFormatted Callback with formatted time string (MM:SS)
 * @param onFinish Callback invoked when countdown completes
 * @param tag Optional tag for identification
 */
fun Any.startFormattedCountdown(
    durationSeconds: Long,
    onTickFormatted: (formattedTime: String, remainingSeconds: Long) -> Unit,
    onFinish: () -> Unit,
    tag: String = this::class.java.simpleName
) {
    startCountdown(
        durationSeconds = durationSeconds,
        onTick = { remaining ->
            val formatted = formatTime(remaining)
            onTickFormatted(formatted, remaining)
        },
        onFinish = onFinish,
        tag = tag
    )
}

/**
 * Format seconds to MM:SS format
 */
private fun formatTime(seconds: Long): String {
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return "%02d:%02d".format(minutes, remainingSeconds)
}

/**
 * Convenient countdown for specific use cases
 */

/**
 * Start a simple countdown that only notifies on completion
 */
fun Any.startSimpleCountdown(
    durationSeconds: Long,
    onFinish: () -> Unit,
    tag: String = this::class.java.simpleName
) {
    startCountdown(
        durationSeconds = durationSeconds,
        onTick = { _ -> }, // Empty tick callback
        onFinish = onFinish,
        tag = tag
    )
}

/**
 * Start a countdown with progress percentage
 */
fun Any.startProgressCountdown(
    durationSeconds: Long,
    onProgress: (progress: Float, remainingSeconds: Long) -> Unit,
    onFinish: () -> Unit,
    tag: String = this::class.java.simpleName
) {
    startCountdown(
        durationSeconds = durationSeconds,
        onTick = { remaining ->
            val progress = 1.0f - (remaining.toFloat() / durationSeconds.toFloat())
            onProgress(progress, remaining)
        },
        onFinish = onFinish,
        tag = tag
    )
}

/**
 * Extension to stop all active countdowns (useful for cleanup)
 */
fun stopAllCountdowns() {
    activeCountdowns.values.forEach { it.cancel() }
    activeCountdowns.clear()
    LogUtils.d("CountdownExtensions", "All countdowns stopped")
}