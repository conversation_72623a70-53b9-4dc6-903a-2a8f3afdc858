package com.example.clean0522.notify

import android.annotation.SuppressLint
import android.app.KeyguardManager
import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.PowerManager
import com.example.clean0522.CleanApp
import com.example.clean0522.utils.PermissionUtils
import java.util.concurrent.TimeUnit

class TipsShowManager {

    companion object {
        const val START_CODE_MSG: Int = 400
        const val CLOSE_CODE_MSG: Int = 401

        const val TIP_FIRST_SHOW_TIME = 60
        const val TIP_NEXT_SHOW_TIME = 50
        const val TIP_MAX_COUNT = 5
        const val TIP_SLIDE_SWITCH = -1
        const val TIP_CLOSE_SWITCH = -1
        const val TIP_LOOP_COUNT = 1

        const val NOTIFICATION_CANCEL_MSG = "cancel_notification"

        private val NOTIFICATION_INTERVAL = TimeUnit.SECONDS.toMillis(4)

        @SuppressLint("StaticFieldLeak")
        private var instance: NotificationHandler? = null

        @Synchronized
        fun getInstance(context: Context): NotificationHandler {
            if (instance == null) {
                instance = NotificationHandler(context)
            }
            return instance!!
        }

        fun closeHandel(){
            if (instance != null) {
                instance?.removeMessages(START_CODE_MSG)
            }
        }

        fun cancelNotification() {
            if (instance != null) {
                instance?.sendEmptyMessage(CLOSE_CODE_MSG)
            }
        }


        fun isScreenOnShow(): Boolean {
            val pm = CleanApp.appContext.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isScreenOn = pm.isInteractive

            val kgm = CleanApp.appContext.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            val isKeyguardLocked = kgm.isKeyguardLocked

            return isScreenOn && !isKeyguardLocked
        }
    }

    class NotificationHandler(private val context: Context) : Handler(Looper.getMainLooper()) {
        private var loopFlag = false
        private var loopCount = 0
        private var showLoopCount = 0
        private var msgData: Notification? = null
        private var showId = -1

        fun startNotificationShow(notification:  Notification, notificationId: Int = -1, maxRepeatCount: Int = 0) {
            this.msgData = notification
            this.showLoopCount = maxRepeatCount
            this.loopFlag = true
            this.loopCount = 0
            this.showId = notificationId
            sendEmptyMessage(START_CODE_MSG)
        }

        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)

            when (msg.what) {
                START_CODE_MSG -> {
                    if (isScreenOnShow() && PermissionUtils.hasNotificationPermission(context) && msgData != null) {
                        try {
                            val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                            manager.notify(showId, msgData)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }

                        removeMessages(START_CODE_MSG)

                        loopCount++
                        if (showLoopCount == -1){
                            loopFlag = true
                        }else if (showLoopCount > 0) {
                            if (loopCount >= showLoopCount) {
                                loopFlag = false
                            }
                        } else {
                            loopFlag = false
                        }

                        if (loopFlag) {
                            sendEmptyMessageDelayed(START_CODE_MSG, NOTIFICATION_INTERVAL)
                        }
                    }
                }

                CLOSE_CODE_MSG -> {
                    closeHandel()
                    try {
                        val manager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                        manager.cancel(showId)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }
}
