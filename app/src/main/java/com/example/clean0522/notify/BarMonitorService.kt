package com.example.clean0522.notify

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import com.example.clean0522.R
import com.example.clean0522.SplashActivity

/**
 * Foreground service for system monitoring that shows a custom notification
 * with access to app features
 */
class BarMonitorService : Service() {

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "system_monitor_channel"
        private const val NOTIFICATION_ID = 1001

        // Action constants for intent handling
        const val ACTION_STOP_SERVICE = "BAR_SERVICE.STOP_SERVICE"

        // Extra constants for navigation
        const val EXTRA_NAVIGATE_TO = "navigate_to"
        const val NAV_HOME = "home"
        const val NAV_ANTIVIRUS = "antivirus"
        const val NAV_RAM = "ram_usage"
        const val NAV_BATTERY = "battery_info"

        // Request code for pending intents
        private const val REQUEST_CODE_HOME = 100
        private const val REQUEST_CODE_ANTIVIRUS = 101
        private const val REQUEST_CODE_RAM = 102
        private const val REQUEST_CODE_BATTERY = 103
        private const val REQUEST_CODE_STOP = 104

        var isServiceRunning = false
    }


    override fun onBind(intent: Intent?): IBinder? {
        return null // Not used for this service
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        isServiceRunning = true
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == ACTION_STOP_SERVICE) {
            stopSelf()
            return START_NOT_STICKY
        }

        try {
            val notification = createNotification()
            startForeground(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return START_STICKY
    }

    override fun onDestroy() {
        isServiceRunning = false
        super.onDestroy()
    }

    /**
     * Creates notification channel for Android O and above
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "BAR_CHANNEL"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Creates custom notification with expanded and collapsed views
     */
    private fun createNotification(): Notification {
        val collapsedView = RemoteViews(packageName, R.layout.notification_collapsed)
        val expandedView = RemoteViews(packageName, R.layout.notification_expanded)

        setupNotificationActions(collapsedView, expandedView)

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.notification_icon)
            .setCustomContentView(collapsedView)
            .setCustomBigContentView(expandedView)
            .setGroup("BAR_GROUND")
            .setOngoing(true)
            .setSilent(true)
            .build()
    }

    /**
     * Sets up notification action buttons for both collapsed and expanded views
     */
    private fun setupNotificationActions(collapsedView: RemoteViews, expandedView: RemoteViews) {
        // Set up home button
        val homePendingIntent = createNavigationPendingIntent(NAV_HOME, REQUEST_CODE_HOME)
        collapsedView.setOnClickPendingIntent(R.id.iv_home, homePendingIntent)
        expandedView.setOnClickPendingIntent(R.id.iv_home, homePendingIntent)
        expandedView.setOnClickPendingIntent(R.id.tv_home, homePendingIntent)

        // Set up antivirus button
        val antivirusPendingIntent = createNavigationPendingIntent(NAV_ANTIVIRUS, REQUEST_CODE_ANTIVIRUS)
        collapsedView.setOnClickPendingIntent(R.id.iv_antivirus, antivirusPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.iv_antivirus, antivirusPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.tv_antivirus, antivirusPendingIntent)

        // Set up RAM button
        val ramPendingIntent = createNavigationPendingIntent(NAV_RAM, REQUEST_CODE_RAM)
        collapsedView.setOnClickPendingIntent(R.id.iv_ram, ramPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.iv_ram, ramPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.tv_ram, ramPendingIntent)

        // Set up battery button
        val batteryPendingIntent = createNavigationPendingIntent(NAV_BATTERY, REQUEST_CODE_BATTERY)
        collapsedView.setOnClickPendingIntent(R.id.iv_battery, batteryPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.iv_battery, batteryPendingIntent)
        expandedView.setOnClickPendingIntent(R.id.tv_battery, batteryPendingIntent)
    }

    /**
     * Creates a pending intent for navigating to a specific feature via SplashActivity
     */
    private fun createNavigationPendingIntent(navigateTo: String, requestCode: Int): PendingIntent {
        // First navigate to splash screen, which will then navigate to MainActivity with the given destination
        val intent = Intent(this, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra(EXTRA_NAVIGATE_TO, navigateTo)
        }

        return PendingIntent.getActivity(
            this,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
}