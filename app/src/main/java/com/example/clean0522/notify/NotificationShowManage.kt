package com.example.clean0522.notify

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.util.Log
import android.view.View
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import com.example.clean0522.CleanApp
import com.example.clean0522.R
import com.example.clean0522.SplashActivity
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.notify.TipsShowManager.Companion.isScreenOnShow
import com.example.clean0522.utils.BatteryInfoUtils
import com.example.clean0522.utils.PermissionUtils
import com.example.clean0522.utils.RamUsageUtils
import com.ext.remoteset.ConfigManager
import kotlin.math.abs
import androidx.core.graphics.toColorInt
import com.ext.firbase.UpdateEvent
import com.ext.remoteset.AnalyticsManager

/**
 * Foreground service for system monitoring that shows a custom notification
 * with access to app features
 */
object NotificationShowManage {

    private const val NOTIFICATION_CHANNEL_ID = "NIT_CHANNEL"
    private const val TIP_PENDING = 500
    private const val TIP_CANCEL_PENDING = 601
    // Extra constants for navigation
    private const val CLICK_PENDING_CODE = 100
    const val EXTRA_NAVIGATE_TO = "navigate_to"
    const val EXTRA_ENTER_CODE = "channel_code"
    const val EXTRA_CHANNEL = "channel"
    const val EXTRA_TIP_TYPE = "tip_type"


    @Synchronized
    fun showTipNotificationHasData(context: Context,data: NotifyData){
        if (isScreenOnShow() && PermissionUtils.hasNotificationPermission(context)
            && NotificationPreferencesManager.getInstance().getNotifyShowCount() < ConfigManager.getTipSettings().tip_max_count){
            val notificationManager = CleanApp.appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            createNotificationChannel(notificationManager)
            val notification = createNotification(context,data)
            try {
                TipsShowManager.getInstance(context).startNotificationShow(notification,data.channel + TIP_PENDING,data.headCount)
                addNotifyCount()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun addNotifyCount(){
        NotificationPreferencesManager.getInstance().apply {
            setNotifyShowTime(System.currentTimeMillis())
            var index = getNotifyIndex()
            NotificationPreferencesManager.getInstance().setNotifyIndex(++ index % ConfigManager.getTipContent().tip_contents.size)
            setNotifyShowCount()
            if (isNotifyFirstShow()){
                setNotifyFirstShow(false)
            }
        }
    }

    fun showTipNotificationByLoop(context: Context) {
        if (isScreenOnShow() && PermissionUtils.hasNotificationPermission(context)){
            if (NotificationPreferencesManager.getInstance().isNotifyFirstShow()){
                if (abs(System.currentTimeMillis() - ComPreferencesManager.instance.getRegisterTime()) >= ConfigManager.getTipSettings().tip_first_time){
                    showTipNotificationHasData(context,ConfigManager.getTipContent().tip_contents[0])
                }
            }else{
                if (abs(System.currentTimeMillis() - NotificationPreferencesManager.getInstance().getNotifyShowTime()) >= ConfigManager.getTipSettings().tip_next_time){
                    val index = NotificationPreferencesManager.getInstance().getNotifyIndex().apply {
                        if (this >= ConfigManager.getTipContent().tip_contents.size){
                            NotificationPreferencesManager.getInstance().setNotifyIndex(0)
                            0
                        }
                    }
                    showTipNotificationHasData(context,ConfigManager.getTipContent().tip_contents[index])
                }
            }
        }
    }

    /**
     * Creates notification channel for Android O and above
     */
    private fun createNotificationChannel(notificationManager: NotificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "BAR_CHANNEL"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Creates custom notification with expanded and collapsed views
     */
    private fun createNotification(context: Context,data: NotifyData): Notification {
        val collapsedView = RemoteViews(context.packageName, R.layout.tip_collapsed)
        val expandedView = RemoteViews(context.packageName, R.layout.tip_expanded)

        val checkPendingIntent = createNavigationPendingIntent(context, data,CLICK_PENDING_CODE + data.channel)
        val cancelPendingIntent = PendingIntent.getBroadcast(
            context, data.channel + TIP_CANCEL_PENDING,
            Intent(TipsShowManager.NOTIFICATION_CANCEL_MSG).apply {
                putExtra(EXTRA_CHANNEL,data.channel + TIP_PENDING)
            },
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
        )

        setupNotificationActions(data,checkPendingIntent,cancelPendingIntent,collapsedView, expandedView)

        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.notification_icon)
            .setCustomContentView(collapsedView)
            .setCustomBigContentView(expandedView)
            .setCustomHeadsUpContentView(expandedView)
            .setContentIntent(checkPendingIntent)
            .setGroup("NIT_GROUND")
            .setAutoCancel(true)
            .setSound(null)
            .setVibrate(null)
            .build()

        if (data.switch_slide >= 0){
            if (abs(System.currentTimeMillis() - ComPreferencesManager.instance.getRegisterTime()) >= data.switch_slide){
                notification.flags = notification.flags or 0x00000002
            }
        }

        return notification
    }

    /**
     * Sets up notification action buttons for both collapsed and expanded views
     */
    private fun setupNotificationActions(data: NotifyData,clickIntent: PendingIntent,cancelIntent: PendingIntent,collapsedView: RemoteViews, expandedView: RemoteViews) {
        val icon = getTypeIcon(data.type)

        val title = when(data.type){
            "RAM" ->{
                if (data.title.contains("r%")){
                    val ramInfo = RamUsageUtils.getCurrentRamUsage(CleanApp.appContext)
                    data.title.replace("r%",ramInfo.usagePercentage.toInt().toString() + "%")
                }else{
                    data.title
                }
            }
            "Battery" ->{
                if (data.title.contains("b%")){
                    val batteryInfo = BatteryInfoUtils.getCurrentBatteryInfo(CleanApp.appContext)
                    data.title.replace("b%",batteryInfo.level.toString() + "%")
                }else{
                    data.title
                }
            }
            else -> data.title
        }

        collapsedView.setOnClickPendingIntent(R.id.btn_check, clickIntent)
        expandedView.setOnClickPendingIntent(R.id.btn_check, clickIntent)

        collapsedView.setTextViewText(R.id.noty_title, title)
        expandedView.setTextViewText(R.id.noty_title, title)

        collapsedView.setTextViewText(R.id.noty_content, data.message)
        expandedView.setTextViewText(R.id.noty_content, data.message)

        collapsedView.setImageViewResource(R.id.noty_icon, icon)
        expandedView.setImageViewResource(R.id.noty_icon, icon)

        try {
            collapsedView.setTextColor(R.id.noty_title, data.tColor.toColorInt())
            expandedView.setTextColor(R.id.noty_title, data.tColor.toColorInt())
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            collapsedView.setTextColor(R.id.noty_content, data.mColor.toColorInt())
            expandedView.setTextColor(R.id.noty_content, data.mColor.toColorInt())
        } catch (e: Exception) {
            e.printStackTrace()
        }

        expandedView.setOnClickPendingIntent(R.id.img_close, cancelIntent)
        if (data.switch_close >= 0){
            if (abs(System.currentTimeMillis() - ComPreferencesManager.instance.getRegisterTime()) >= data.switch_close){
                expandedView.setViewVisibility(R.id.img_close, View.VISIBLE)
            }
        }

    }

    private fun getNavigateTo(type: String): String {
        return when (type) {
            "RAM" -> "ram_usage"
            "Battery" -> "battery_info"
            "Storage" -> "device_storage"
            "Antivirus" -> "security_scan"
            "Large" -> "large_files"
            "Recent" -> "recent_files"
            "Redundant" -> "redundant_files"
            "App" -> "app_manager"
            else -> ""
        }
    }

    private fun getTypeIcon(type: String): Int{
        UpdateEvent.event("tip_post_all")
        return when (type) {
            "RAM" -> {
                AnalyticsManager.sendTipPost("post_ram")
                UpdateEvent.event("tip_post_ram")
                R.mipmap.tool_ram
            }
            "Battery" -> {
                AnalyticsManager.sendTipPost("post_battery")
                UpdateEvent.event("tip_post_battery")
                R.mipmap.tool_battery
            }
            "Storage" -> {
                AnalyticsManager.sendTipPost("post_storage")
                UpdateEvent.event("tip_post_storage")
                R.mipmap.tool_storage
            }
            "Antivirus" -> {
                AnalyticsManager.sendTipPost("post_antivirus")
                UpdateEvent.event("tip_post_antivirus")
                R.mipmap.tool_anti
            }
            "Large" -> {
                AnalyticsManager.sendTipPost("post_large")
                UpdateEvent.event("tip_post_large")
                R.mipmap.ic_large
            }
            "Recent" -> {
                AnalyticsManager.sendTipPost("post_recent")
                UpdateEvent.event("tip_post_recent")
                R.mipmap.ic_recent
            }
            "Redundant" -> {
                AnalyticsManager.sendTipPost("post_redundant")
                UpdateEvent.event("tip_post_redundant")
                R.mipmap.ic_redundant
            }
            "App" -> {
                AnalyticsManager.sendTipPost("post_app")
                UpdateEvent.event("tip_post_app")
                R.mipmap.tool_app_ma
            }
            else -> {
                AnalyticsManager.sendTipPost("post_other")
                UpdateEvent.event("tip_post_other")
                R.mipmap.tool_ram
            }
        }
    }

    /**
     * Creates a pending intent for navigating to a specific feature via SplashActivity
     */
    private fun createNavigationPendingIntent(context: Context,data: NotifyData, requestCode: Int): PendingIntent {
        val intent = Intent(context, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra(EXTRA_NAVIGATE_TO, getNavigateTo(data.type))
            putExtra(EXTRA_ENTER_CODE, data.enter)
            putExtra(EXTRA_CHANNEL, data.channel + TIP_PENDING)
            putExtra(EXTRA_TIP_TYPE, data.type)
        }

        return PendingIntent.getActivity(
            context,
            requestCode,
            intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_UPDATE_CURRENT
        )
    }
}