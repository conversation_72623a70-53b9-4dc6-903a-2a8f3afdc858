package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV

/**
 * Manager for notification preferences using MMKV
 */
class NotificationPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
        private const val KEY_NOTIFICATION_LAST = "notification_last"
        private const val KEY_NOTIFICATION_COUNT = "notification_count"
        private const val KEY_NOTIFY_INDEX = "notify_index"
        private const val KEY_NOTIFY_SHOW_TIME = "notify_show_time"
        private const val KEY_NOTIFY_FIRST_SHOW = "notify_first_show"
        private const val KEY_NOTIFY_SHOW_COUNT = "notify_show_count"
        private const val KEY_TOPIC_FLAG = "topic_flag"

        @Volatile
        private var INSTANCE: NotificationPreferencesManager? = null
        
        fun getInstance(): NotificationPreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set notification enabled state in MMKV
     * This is the user preference, separate from system permission
     */
    fun setNotificationEnabled(enabled: Boolean) {
        mmkv.putBoolean(KEY_NOTIFICATION_ENABLED, enabled)
    }
    
    /**
     * Get notification enabled state from MMKV
     * Default is true (enabled)
     */
    fun isNotificationEnabled(): Boolean {
        return mmkv.getBoolean(KEY_NOTIFICATION_ENABLED, true)
    }

    fun getLastNotificationTime(): Long {
        return mmkv.getLong(KEY_NOTIFICATION_LAST, 0)
    }

    fun setLastNotificationTime(time: Long) {
        mmkv.putLong(KEY_NOTIFICATION_LAST, time)
    }

    fun getNotificationCount(): Int {
        return mmkv.getInt(KEY_NOTIFICATION_COUNT, 0)
    }

    fun setNotificationCount() {
        var count = getNotificationCount()
        mmkv.putInt(KEY_NOTIFICATION_COUNT, ++ count)
    }

    fun getNotifyIndex(): Int {
        return mmkv.getInt(KEY_NOTIFY_INDEX, 0)
    }

    fun setNotifyIndex(index: Int) {
        mmkv.putInt(KEY_NOTIFY_INDEX, index)
    }

    fun getNotifyShowTime(): Long {
        return mmkv.getLong(KEY_NOTIFY_SHOW_TIME, 0)
    }

    fun setNotifyShowTime(time: Long) {
        mmkv.putLong(KEY_NOTIFY_SHOW_TIME, time)
    }

    fun isNotifyFirstShow(): Boolean {
        return mmkv.getBoolean(KEY_NOTIFY_FIRST_SHOW, true)
    }

    fun setNotifyFirstShow(flag: Boolean) {
        mmkv.putBoolean(KEY_NOTIFY_FIRST_SHOW, flag)
    }

    fun setTopicFlag(flag: Boolean) {
        mmkv.putBoolean(KEY_TOPIC_FLAG, flag)
    }

    fun getTopicFlag(): Boolean {
        return mmkv.getBoolean(KEY_TOPIC_FLAG, false)
    }

    fun getNotifyShowCount(): Int {
        return mmkv.getInt(KEY_NOTIFY_SHOW_COUNT, 0)
    }

    fun setNotifyShowCount(setCount: Int = -1) {
        var count = getNotifyShowCount()
        if (setCount >= 0){
            mmkv.putInt(KEY_NOTIFY_SHOW_COUNT,setCount)
        }else{
            mmkv.putInt(KEY_NOTIFY_SHOW_COUNT, ++ count)
        }
    }
    
    /**
     * Clear all notification preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_NOTIFICATION_ENABLED)
    }
}
