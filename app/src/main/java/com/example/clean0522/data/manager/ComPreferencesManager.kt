package com.example.clean0522.data.manager

import com.tencent.mmkv.MMKV
import java.util.Calendar
import java.util.TimeZone

/**
 * Manager for terms and privacy agreement preferences using MMKV
 */
class ComPreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_TERMS_ACCEPTED = "terms_accepted"
        private const val KEY_GUIDE_SHOW = "guide_show"
        private const val KEY_REGISTER_TIME = "register_time"
        private const val KEY_LAST_CHECKED_DAY = "last_checked_day"
        private const val KEY_SCORE_LAST_TIME = "score_last_time"
        private const val KEY_SCORE_COUNT = "score_count"
        private const val KEY_RATE_SHOW = "rate_show"

        @Volatile
        private var INSTANCE: ComPreferencesManager? = null

        val instance: ComPreferencesManager by lazy {
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: ComPreferencesManager().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Set terms and privacy agreement accepted state
     */
    fun setTermsAccepted(accepted: Boolean) {
        mmkv.putBoolean(KEY_TERMS_ACCEPTED, accepted)
    }
    
    /**
     * Get terms and privacy agreement accepted state
     * Default is false (not accepted)
     */
    fun isTermsAccepted(): Boolean {
        return mmkv.getBoolean(KEY_TERMS_ACCEPTED, false)
    }

    fun setShowGuide(accepted: Boolean) {
        mmkv.putBoolean(KEY_GUIDE_SHOW, accepted)
    }

    fun isShowGuide(): Boolean {
        return mmkv.getBoolean(KEY_GUIDE_SHOW, false)
    }

    fun setRegisterTime(time: Long) {
        mmkv.putLong(KEY_REGISTER_TIME, time)
    }

    fun getRegisterTime(): Long {
        return mmkv.getLong(KEY_REGISTER_TIME, 0)
    }

    fun setScoreLastTime(time: Long) {
        mmkv.putLong(KEY_SCORE_LAST_TIME, time)
    }

    fun getScoreLastTime(): Long {
        return mmkv.getLong(KEY_SCORE_LAST_TIME, 0)
    }

    fun setScoreCount() {
        var count = getScoreCount()
        mmkv.putInt(KEY_SCORE_COUNT, ++ count)
    }

    fun getScoreCount(): Int {
        return mmkv.getInt(KEY_SCORE_COUNT, 0)
    }

    fun setRateShow(accepted: Boolean) {
        mmkv.putBoolean(KEY_RATE_SHOW, accepted)
    }

    fun isRateShow(): Boolean {
        return mmkv.getBoolean(KEY_RATE_SHOW, false)
    }
    
    /**
     * Clear all terms preferences
     */
    fun clearPreferences() {
        mmkv.remove(KEY_TERMS_ACCEPTED)
    }

    fun isNewDay(): Boolean {
        val cal = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        val currentDayStart = cal.timeInMillis

        val lastDayStart = mmkv.getLong(KEY_LAST_CHECKED_DAY, 0)
        return if (currentDayStart != lastDayStart) {
            mmkv.putLong(KEY_LAST_CHECKED_DAY, currentDayStart)
            true
        } else false
    }
}
