package com.example.clean0522.domain.model

/**
 * Represents threat information from antivirus scan
 */
data class ThreatInfo(
    val id: String, // Unique identifier (package name or file path)
    val appName: String,
    val packageName: String?,
    val filePath: String?,
    val md5: String,
    val score: Int,
    val virusName: String,
    val category: String,
    val summary: Array<String>,
    val isFromStatic: Boolean
) {
    /**
     * Get threat level based on score
     */
    val threatLevel: ThreatLevel
        get() = when {
            score >= 8 -> ThreatLevel.MALWARE
            score >= 6 -> ThreatLevel.PUA
            else -> ThreatLevel.SAFE
        }

    /**
     * Get display name (app name or file name)
     */
    val displayName: String
        get() = appName.ifBlank { filePath?.substringAfterLast('/') ?: "Unknown" }

    /**
     * Get display path (package name or file path)
     */
    val displayPath: String
        get() = packageName ?: filePath ?: ""

    /**
     * Check if this threat represents an installed app (not an APK file)
     * Based on file path location rather than just package name existence
     */
    val isInstalledApp: Boolean
        get() = filePath?.let { path ->
            // Check if the file path is in app installation directories
            path.startsWith("/data/app/") ||
            path.startsWith("/system/app/") ||
            path.startsWith("/system/priv-app/")
        } ?: false

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ThreatInfo

        if (id != other.id) return false
        if (appName != other.appName) return false
        if (packageName != other.packageName) return false
        if (filePath != other.filePath) return false
        if (md5 != other.md5) return false
        if (score != other.score) return false
        if (virusName != other.virusName) return false
        if (category != other.category) return false
        if (!summary.contentEquals(other.summary)) return false
        if (isFromStatic != other.isFromStatic) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + appName.hashCode()
        result = 31 * result + (packageName?.hashCode() ?: 0)
        result = 31 * result + (filePath?.hashCode() ?: 0)
        result = 31 * result + md5.hashCode()
        result = 31 * result + score
        result = 31 * result + virusName.hashCode()
        result = 31 * result + category.hashCode()
        result = 31 * result + summary.contentHashCode()
        result = 31 * result + isFromStatic.hashCode()
        return result
    }
}

/**
 * Threat level enumeration
 */
enum class ThreatLevel(val displayName: String, val colorName: String) {
    MALWARE("Malware", "red"),
    PUA("PUA", "yellow"),
    SAFE("Safe", "green")
}

/**
 * Scan state enumeration
 */
enum class ScanState {
    IDLE,
    SCANNING,
    COMPLETED,
    ERROR,
    CANCELLED
}

/**
 * Scan result data class
 */
data class ScanResult(
    val totalScanned: Int = 0,
    val threatsFound: List<ThreatInfo> = emptyList(),
    val scanState: ScanState = ScanState.IDLE,
    val errorMessage: String? = null,
    val progress: Int = 0
) {
    val threatCount: Int get() = threatsFound.size
}
