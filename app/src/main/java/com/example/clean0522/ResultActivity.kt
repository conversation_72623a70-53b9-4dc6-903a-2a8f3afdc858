package com.example.clean0522

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.example.clean0522.ui.features.antivirus.FeatureRecommendationCard
import com.example.clean0522.ui.features.antivirus.getFeatureRecommendations
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.LottieAnimationView
import com.example.clean0522.utils.startCountdown
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

class ResultActivity: ComponentBaseActivity() {
    companion object {
        const val EXTRA_RESULT_TYPE = "result_type"

        fun startResultActivity(context: Context,type: String){
            val intent = Intent(context, ResultActivity::class.java).apply {
                putExtra(EXTRA_RESULT_TYPE, type)
            }
            context.startActivity(intent)
            try {
                if (context as Activity !is MainActivity){
                    context.finish()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private var resultType by mutableStateOf("")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        resultType = intent.getStringExtra(EXTRA_RESULT_TYPE) ?: ""
    }

    @Composable
    override fun setHomePage() {
        Clean0522Theme {
            ResultScreen(
                onBackClick = { finish() }
            )
        }
    }

    @Composable
    fun ResultScreen(
        onBackClick: () -> Unit
    ) {
        var showAnimate by remember { mutableStateOf(false) }

        LaunchedEffect(Unit) {
            showAnimate = true
            startCountdown(
                onTick = { _ -> },
                onFinish = { showAnimate = false }
            )
        }

        Scaffold(
            containerColor = colorResource(R.color.bg_color),
            modifier = Modifier.navigationBarsPadding(),
            topBar = {
                TopNavBar(
                    title = stringResource(R.string.result_title),
                    showBackButton = true,
                    backButtonAction = onBackClick
                )
            },
            bottomBar = {
                if (AdFusionConfig.openBanner() && !showAnimate){
                    AdFusionController.getBannerAd().BannerAdView()
                }
            }
        ){ paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(painter = painterResource(R.mipmap.img_comp),
                    contentDescription = null,
                    modifier = Modifier.size(120.dp))

                AdFusionController.getNativeAd().NativeAdView()

                val featureRecommendations = getFeatureRecommendations().filter { it.id != resultType }

                for (feature in featureRecommendations) {
                    FeatureRecommendationCard(
                        feature = feature,
                        onFeatureClick = { navigateToFeature(feature.id) }
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }

        if (showAnimate){
            Box(modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center){
                LottieAnimationView(
                    type = "trash"
                )
            }
        }
    }
}