package com.example.clean0522

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.ui.theme.Clean0522Theme
import kotlinx.coroutines.delay
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import com.example.clean0522.notify.NotificationShowManage
import com.example.clean0522.utils.startCountdown
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.AppOpenAdAdapter
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.util.AdEventManager
import kotlinx.coroutines.launch

/**
 * Splash screen activity
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : ComponentActivity() {

    private var intersAdInstance: InterstitialAdAdapter? = null
    private var openAdInstance: AppOpenAdAdapter? = null
    private var openAdFlag = true
    private var adIsLoaded = false
    private var adIsShowed = false
    private var adStartInit  = false
    private var splashEnter = false
    private var jumpAd = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val navigateTo = intent.getStringExtra(NotificationShowManage.EXTRA_NAVIGATE_TO)
        val code = intent.getIntExtra(NotificationShowManage.EXTRA_ENTER_CODE, 2)
        splashEnter = intent.getBooleanExtra("splash_enter", false)
        intent.removeExtra("splash_enter")
        intent.removeExtra(NotificationShowManage.EXTRA_ENTER_CODE)

        openAdFlag = if (!ComPreferencesManager.instance.isTermsAccepted()){
            false
        }else{
            when (code) {
                1 -> { jumpAd = true; true}
                2 -> { true }
                else -> { false }
            }
        }

        lifecycleScope.launch {
            AdEventManager.adEventFlow.collect { event ->
                initInterstitial(openAdFlag)
            }
        }

        initInterstitial(openAdFlag)

        setContent {
            BackHandler {  }
            Clean0522Theme {
                SplashScreen(
                    jumpAd = jumpAd,
                    onAcceptTerms = {
                    },
                    onLoadingComplete = {
                        startCountdown(
                            durationSeconds = if (!jumpAd) if (openAdFlag) AdFusionConfig.openLoadTime
                            else AdFusionConfig.intersLoadTime else 1,
                            onTick = { _ ->
                                if (AdFusionConfig.allowShowAd() && adIsLoaded && !adIsShowed){
                                    if (openAdFlag){
                                        openAdInstance?.showAd(this@SplashActivity,
                                            onAdDismissed = {
                                                navigateToMain(navigateTo)
                                            })
                                    }else{
                                        intersAdInstance?.showAd(this@SplashActivity,
                                            onAdDismissed = {
                                                navigateToMain(navigateTo)
                                            })
                                    }
                                }
                            },
                            onFinish = {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    if (!adIsShowed){
                                        navigateToMain(navigateTo)
                                    }
                                }, 300)
                            }
                        )
                    }
                )
            }
        }
    }

    fun initInterstitial(flag: Boolean){
        if (adStartInit || !AdFusionController.sdkIsInit()) return
        adStartInit = true
        openAdFlag = flag
        if (openAdFlag){
            openAdInstance = AdFusionController.getAppOpenAd(this).apply {
                setAdListener(object : AdEventListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        adIsLoaded = true
                    }

                    override fun onAdDisplay() {
                        super.onAdDisplay()
                        adIsShowed = true
                    }
                })
                loadAd()
            }
        }else {
            intersAdInstance = AdFusionController.getInterstitialAd(this).apply {
                setAdListener(object : AdEventListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        adIsLoaded = true
                    }

                    override fun onAdDisplay() {
                        super.onAdDisplay()
                        adIsShowed = true
                    }
                })
                loadAd()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        intersAdInstance?.onDestroy()
        openAdInstance?.onDestroy()
        AdFusionController.getAppOpenAd(this).loadAd()
    }

    private fun navigateToMain(navigateTo: String?) {
        if (!splashEnter){
            val intent = Intent(this, MainActivity::class.java)
            if (navigateTo != null) {
                intent.putExtra(NotificationShowManage.EXTRA_NAVIGATE_TO, navigateTo)
            }
            try {
                startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        finish()
    }
}

/**
 * Splash screen composable
 */
@Composable
fun SplashScreen(
    jumpAd: Boolean = false,
    onAcceptTerms: () -> Unit,
    onLoadingComplete: () -> Unit
) {
    val termsManager = remember { ComPreferencesManager.instance }
    val isTermsAccepted = remember { termsManager.isTermsAccepted() }

    var showLoading by remember { mutableStateOf(isTermsAccepted) }

    val progress = remember { Animatable(0f) }

    LaunchedEffect(showLoading) {
        if (showLoading) {
            delay(20)

            val second = if (jumpAd) 1000 else 4000

            val progressSteps = listOf(
                25f to 0.2 * second,
                60f to 0.25 * second,
                85f to 0.3 * second,
                100f to 0.25 * second
            )

            for ((targetProgress, duration) in progressSteps) {
                progress.animateTo(
                    targetValue = targetProgress,
                    animationSpec = tween(
                        durationMillis = duration.toInt(),
                        easing = LinearEasing
                    )
                )
            }

            // Complete loading
            onLoadingComplete()
        }
    }

    val handleAcceptTerms = {
        termsManager.setTermsAccepted(true)
        showLoading = true
        onAcceptTerms()
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(painter = painterResource(R.drawable.bg_splash), contentScale = ContentScale.Crop)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            Spacer(modifier = Modifier.weight(1f))

            Image(
                painter = painterResource(id = R.mipmap.img_logo),
                contentDescription = "App Icon",
                modifier = Modifier.size(120.dp)
                    .border(width = 2.dp, color = Color.White, shape = RoundedCornerShape(12.dp))
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Text(
                text = stringResource(R.string.app_name),
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.weight(1f))
            
            Box(modifier = Modifier.heightIn(min = 100.dp)){
                if (!showLoading) {
                    TermsAcceptanceContent(
                        onAccept = handleAcceptTerms
                    )
                } else {
                    LoadingContent(
                        progress = progress.value
                    )
                }
            }

            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * Terms acceptance content
 */
@Composable
fun TermsAcceptanceContent(
    onAccept: () -> Unit
) {
    val context = LocalContext.current

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Accept button
        Button(
            onClick = onAccept,
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = stringResource(R.string.accept),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_blue)
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Terms and privacy policy text with clickable links
        val termsText = stringResource(R.string.terms_of_use_link)
        val privacyText = stringResource(R.string.privacy_policy_link)
        val baseMessage = stringResource(R.string.splash_terms_message)
        val fullText = baseMessage.format(termsText, privacyText)

        val defaultTextStyle = SpanStyle(
            color = Color.White,
            fontSize = 15.sp,
            fontWeight = FontWeight.Medium
        )

        val annotatedString = buildAnnotatedString {
            val linkStyle = SpanStyle(
                color = Color(0xFFFEFE89),
                fontSize = 15.sp,
                textDecoration = TextDecoration.Underline
            )

            withStyle(defaultTextStyle) {
                val termsStart = fullText.indexOf(termsText)
                val privacyStart = fullText.indexOf(privacyText)
                var currentIndex = 0

                if (termsStart > 0) {
                    append(fullText.substring(currentIndex, termsStart))
                    currentIndex = termsStart
                }

                if (termsStart >= 0) {
                    pushStringAnnotation(
                        tag = "TERMS_URL",
                        annotation = stringResource(R.string.terms_of_use_url)
                    )
                    withStyle(linkStyle) {
                        append(termsText)
                    }
                    pop()
                    currentIndex = termsStart + termsText.length
                }

                if (privacyStart > currentIndex) {
                    append(fullText.substring(currentIndex, privacyStart))
                    currentIndex = privacyStart
                }

                if (privacyStart >= 0) {
                    pushStringAnnotation(
                        tag = "PRIVACY_URL",
                        annotation = stringResource(R.string.privacy_policy_url)
                    )
                    withStyle(linkStyle) {
                        append(privacyText)
                    }
                    pop()
                    currentIndex = privacyStart + privacyText.length
                }

                if (currentIndex < fullText.length) {
                    append(fullText.substring(currentIndex))
                }
            }
        }

        ClickableText(
            text = annotatedString,
            style = LocalTextStyle.current.copy(
                fontSize = 14.sp,
                color = Color.White.copy(alpha = 0.8f),
                textAlign = TextAlign.Center,
                lineHeight = 20.sp
            ),
            onClick = { offset ->
                // Handle terms link click
                annotatedString.getStringAnnotations(
                    tag = "TERMS_URL",
                    start = offset,
                    end = offset
                ).firstOrNull()?.let { annotation ->
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, annotation.item.toUri())
                        context.startActivity(intent)
                    } catch (e: Exception) {
                        // Handle error if no browser app is available
                    }
                }

                // Handle privacy link click
                annotatedString.getStringAnnotations(
                    tag = "PRIVACY_URL",
                    start = offset,
                    end = offset
                ).firstOrNull()?.let { annotation ->
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, annotation.item.toUri())
                        context.startActivity(intent)
                    } catch (e: Exception) {
                        // Handle error if no browser app is available
                    }
                }
            }
        )
    }
}

/**
 * Loading content with custom progress bar using Box
 */
@Composable
fun LoadingContent(
    progress: Float
) {
    // Ensure progress is within 0-100 range
    val clampedProgress = progress.coerceIn(0f, 100f)

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Progress percentage text (0-100)
        Text(
            text = "${clampedProgress.toInt()}%",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Custom progress bar using Box
        CustomProgress(clampedProgress)

    }
}

@Composable
fun CustomProgress(
    clampedProgress: Float,
    width: Dp = 0.dp,
    height: Dp = 12.dp,
    backgroundColor: Color = Color.White.copy(alpha = 0.3f),
    progressColor: Color = Color.White){
    Box(
        modifier = if (width != 0.dp){
            Modifier
                .width(width)
                .height(height)
                .background(
                    backgroundColor,
                    shape = RoundedCornerShape(7.dp)
                )
        }else{
            Modifier
                .fillMaxWidth()
                .height(height)
                .background(
                    backgroundColor,
                    shape = RoundedCornerShape(7.dp)
                )
        }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth(clampedProgress / 100f)
                .fillMaxHeight()
                .background(
                    color = progressColor,
                    shape = RoundedCornerShape(7.dp)
                )
        )
    }
}
