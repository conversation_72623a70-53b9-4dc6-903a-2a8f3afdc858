package com.example.clean0522

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.clean0522.ui.features.browser.ApksBrowserContent
import com.example.clean0522.ui.features.browser.AudiosBrowserContent
import com.example.clean0522.ui.features.browser.DocsBrowserContent
import com.example.clean0522.ui.features.browser.ImagesBrowserContent
import com.example.clean0522.ui.features.browser.MediaBrowserViewModel
import com.example.clean0522.ui.features.browser.StorageBrowserContent
import com.example.clean0522.ui.features.browser.StorageBrowserViewModel
import com.example.clean0522.ui.features.browser.VideosBrowserContent
import com.example.clean0522.ui.features.browser.ZipsBrowserContent
import com.example.clean0522.ui.navigation.Screen
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.ui.components.CustomCheckbox
import com.example.clean0522.ui.components.FileOperationBar
import com.example.clean0522.ui.components.RemoveButton
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.LottieAnimationUtils
import com.example.clean0522.utils.LottieAnimationView
import com.example.clean0522.utils.startCountdown
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.InterstitialAdAdapter

/**
 * Activity for file browser screens
 */
class FileBrowserActivity : ComponentBaseActivity() {

    companion object {
        const val EXTRA_BROWSER_TYPE = "browser_type"
        const val TYPE_STORAGE = "storage"
        const val TYPE_IMAGES = "images"
        const val TYPE_VIDEOS = "videos"
        const val TYPE_AUDIOS = "audios"
        const val TYPE_DOCS = "docs"
        const val TYPE_ZIPS = "zips"
        const val TYPE_APKS = "apks"
    }

    @Composable
    override fun setHomePage() {
        val browserType = intent.getStringExtra(EXTRA_BROWSER_TYPE) ?: TYPE_STORAGE
        Clean0522Theme {
            FileBrowserScreen(
                browserType = browserType,
                onBackClick = {
                    finish()
                }
            )
        }
    }
}

/**
 * File browser screen composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FileBrowserScreen(
    browserType: String,
    onBackClick: () -> Unit
) {
    val navController = rememberNavController()
    var showAnimate by remember { mutableStateOf(true) }

    val context = LocalContext.current
    var intersAdInstance: InterstitialAdAdapter? = null
    var adIsLoaded = false
    var adIsShowed = false

    intersAdInstance = AdFusionController.getInterstitialAd(context).apply {
        setAdListener(object : AdEventListener() {
            override fun onAdLoaded() {
                super.onAdLoaded()
                adIsLoaded = true
            }

            override fun onAdDisplay() {
                super.onAdDisplay()
                adIsShowed = true
            }
        })
        loadAd()
    }


    val lifecycleOwner = LocalLifecycleOwner.current

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    intersAdInstance?.onDestroy()
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(Unit) {
        showAnimate = true
        startCountdown(
            durationSeconds = AdFusionConfig.intersLoadTime,
            onTick = {
                if (AdFusionConfig.allowShowAd() && adIsLoaded && !adIsShowed && it > 2){
                    intersAdInstance.showAd(context as Activity,
                        onAdDismissed = {
                            showAnimate = false
                        })
                }
            },
            onFinish = { showAnimate = false }
        )
    }

    if (browserType == FileBrowserActivity.TYPE_STORAGE){
        val storageBrowserViewMode: StorageBrowserViewModel = viewModel()
        val isSelectionMode = storageBrowserViewMode.isSelectionMode

        BackHandler {
            if (isSelectionMode) {
                storageBrowserViewMode.exitSelectionMode()
            }else{
                if (!storageBrowserViewMode.isRootDirectory()){
                    storageBrowserViewMode.navigateToParentDirectory()
                }else{
                    onBackClick()
                }
            }
        }

        Scaffold(
            modifier = Modifier.navigationBarsPadding(),
            containerColor = colorResource(R.color.bg_color),
            topBar = {
                TopNavBar(
                    title = stringResource(R.string.browser_storage),
                    showBackButton = true,
                    backButtonAction = {
                        if (isSelectionMode) {
                            storageBrowserViewMode.exitSelectionMode()
                        }else{
                            if (!storageBrowserViewMode.isRootDirectory()){
                                storageBrowserViewMode.navigateToParentDirectory()
                            }else{
                                onBackClick()
                            }
                        }
                    },
                    settingsButtonContent = {
                        if (!showAnimate){
                            if (isSelectionMode) {
                                val fileList = storageBrowserViewMode.fileList
                                val selectedFiles = storageBrowserViewMode.selectedFiles
                                val allSelected = fileList.isNotEmpty() && selectedFiles.size == fileList.size

                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Text(
                                        text = stringResource(R.string.tab_all),
                                        modifier = Modifier.padding(end = 4.dp),
                                        colorResource(R.color.text_black),
                                        fontSize = 18.sp,
                                        fontWeight = FontWeight.SemiBold
                                    )
                                    CustomCheckbox(
                                        checked = allSelected,
                                        onCheckedChange = { storageBrowserViewMode.toggleSelectAll() },
                                        size = 18.dp
                                    )
                                }

                            } else {
                                // Show sort button in normal mode
                                Image(painter = painterResource(R.mipmap.ic_sort),
                                    contentDescription = stringResource(R.string.sort),
                                    modifier = Modifier.size(24.dp)
                                        .clickable {
                                            storageBrowserViewMode.showSortDialog()
                                        }
                                )
                            }
                        }
                    }
                )
            },
            bottomBar = {
                Column(modifier = Modifier.fillMaxWidth()) {
                    if (isSelectionMode) {
                        StorageBrowserBottomBar(viewModel = storageBrowserViewMode)
                    }
                    if (!showAnimate){
                        AdFusionController.getNativeAd().NativeAdView()
                    }
                }
            }
        ) { innerPadding ->
            Box(modifier = Modifier.padding(innerPadding)){
                StorageBrowserContent(storageBrowserViewMode)

                if (showAnimate){
                    LottieAnimationView(
                        type = FileBrowserActivity.TYPE_STORAGE,
                        modifier = Modifier
                    )
                }
            }
        }
    }else{
        val mediaBrowserViewModel: MediaBrowserViewModel = viewModel()
        val mediaSelectionMode = mediaBrowserViewModel.isSelectionMode
        BackHandler {
            if (mediaSelectionMode) {
                mediaBrowserViewModel.exitSelectionMode()
            }else{
                onBackClick()
            }
        }

        Scaffold(
            containerColor = colorResource(R.color.bg_color),
            modifier = Modifier.navigationBarsPadding(),
            topBar = {
                Column {
                    TopNavBar(
                        title = when (browserType) {
                            FileBrowserActivity.TYPE_IMAGES -> stringResource(R.string.browser_images)
                            FileBrowserActivity.TYPE_VIDEOS -> stringResource(R.string.browser_videos)
                            FileBrowserActivity.TYPE_AUDIOS -> stringResource(R.string.browser_audios)
                            FileBrowserActivity.TYPE_DOCS -> stringResource(R.string.browser_docs)
                            FileBrowserActivity.TYPE_ZIPS -> stringResource(R.string.browser_zips)
                            FileBrowserActivity.TYPE_APKS -> stringResource(R.string.browser_apks)
                            else -> stringResource(R.string.browser_file)},

                        showBackButton = true,
                        backButtonAction = {
                            if (mediaSelectionMode) {
                                mediaBrowserViewModel.exitSelectionMode()
                            }else{
                                onBackClick()
                            }
                        },
                        settingsButtonContent = {
                            if (!showAnimate){
                                if (mediaSelectionMode) {
                                    val allFiles = getAllFilesForBrowserType(browserType, mediaBrowserViewModel)
                                    val selectedFiles = mediaBrowserViewModel.selectedFiles
                                    val allSelected = allFiles.isNotEmpty() && selectedFiles.size == allFiles.size


                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(
                                            text = stringResource(R.string.tab_all),
                                            modifier = Modifier.padding(end = 4.dp),
                                            colorResource(R.color.text_black),
                                            fontSize = 18.sp,
                                            fontWeight = FontWeight.SemiBold
                                        )
                                        CustomCheckbox(
                                            checked = allSelected,
                                            onCheckedChange = {
                                                mediaBrowserViewModel.toggleSelectAll(allFiles)
                                            },
                                            size = 18.dp
                                        )
                                    }
                                } else {
                                    if (browserType != FileBrowserActivity.TYPE_IMAGES &&
                                        browserType != FileBrowserActivity.TYPE_VIDEOS) {
                                        Image(painter = painterResource(R.mipmap.ic_sort),
                                            contentDescription = stringResource(R.string.sort),
                                            modifier = Modifier.size(24.dp)
                                                .clickable {
                                                    mediaBrowserViewModel.showSortDialog()
                                                }
                                        )
                                    }
                                }
                            }
                        }
                    )
                    if (AdFusionConfig.openBanner() && !showAnimate){
                        AdFusionController.getBannerAd().BannerAdView()
                    }
                }
            },
            bottomBar = {
                Column(modifier = Modifier.fillMaxWidth()) {
                    if (mediaSelectionMode) {
                        MediaBrowserBottomBar(viewModel = mediaBrowserViewModel)
                    }
                    if (!showAnimate){
                        AdFusionController.getNativeAd().NativeAdView()
                    }
                }
            }
        ) { innerPadding ->
            Box(modifier = Modifier.padding(innerPadding)) {
                FileBrowserNavHost(
                    navController = navController,
                    browserType = browserType
                )

                if (showAnimate){
                    LottieAnimationView(
                        type = LottieAnimationUtils.AnimationType.SEARCH.fileName,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
    }
}

/**
 * Navigation host for file browser screens
 */
@Composable
fun FileBrowserNavHost(
    navController: NavHostController,
    mediaBrowserViewModel: MediaBrowserViewModel = viewModel(),
    browserType: String
) {
    val startDestination = when (browserType) {
        FileBrowserActivity.TYPE_STORAGE -> Screen.StorageBrowser.route
        FileBrowserActivity.TYPE_IMAGES -> Screen.ImagesBrowser.route
        FileBrowserActivity.TYPE_VIDEOS -> Screen.VideosBrowser.route
        FileBrowserActivity.TYPE_AUDIOS -> Screen.AudiosBrowser.route
        FileBrowserActivity.TYPE_DOCS -> Screen.DocsBrowser.route
        FileBrowserActivity.TYPE_ZIPS -> Screen.ZipsBrowser.route
        FileBrowserActivity.TYPE_APKS -> Screen.ApksBrowser.route
        else -> Screen.StorageBrowser.route
    }

    Box(modifier = Modifier
        .padding(horizontal = 16.dp, vertical = 8.dp)){
        NavHost(
            navController = navController,
            startDestination = startDestination
        ) {
            composable(Screen.ImagesBrowser.route) {
                ImagesBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.VideosBrowser.route) {
                VideosBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.AudiosBrowser.route) {
                AudiosBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.DocsBrowser.route) {
                DocsBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.ZipsBrowser.route) {
                ZipsBrowserContent(mediaBrowserViewModel)
            }

            composable(Screen.ApksBrowser.route) {
                ApksBrowserContent(mediaBrowserViewModel)
            }
        }
    }
}

/**
 * Get all files for the current browser type
 */
private fun getAllFilesForBrowserType(
    browserType: String,
    viewModel: MediaBrowserViewModel
): List<com.example.clean0522.domain.model.MediaFile> {
    return when (browserType) {
        FileBrowserActivity.TYPE_IMAGES -> viewModel.imageGroups.flatMap { it.files }
        FileBrowserActivity.TYPE_VIDEOS -> viewModel.videoGroups.flatMap { it.files }
        FileBrowserActivity.TYPE_AUDIOS -> viewModel.audioFiles
        FileBrowserActivity.TYPE_DOCS -> viewModel.filteredDocumentFiles
        FileBrowserActivity.TYPE_ZIPS -> viewModel.archiveFiles
        FileBrowserActivity.TYPE_APKS -> viewModel.apkFiles
        else -> emptyList()
    }
}

/**
 * Bottom bar for storage browser operations
 */
@Composable
fun StorageBrowserBottomBar(viewModel: StorageBrowserViewModel) {
    val context = LocalContext.current

    if (viewModel.selectedFiles.size == 1) {
        FileOperationBar(
            onDetailClick = { viewModel.showFileDetails() },
            onOpenClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    if (!fileItem.isDirectory) {
                        FileUtils.openFile(context, fileItem.file)
                    }
                }
            },
            onShareClick = {
                viewModel.currentSelectedFile?.let { fileItem ->
                    if (!fileItem.isDirectory) {
                        FileUtils.shareFile(context, fileItem.file)
                    }
                }
            },
            onRenameClick = { viewModel.showRenameFile() },
            onRemoveClick = { viewModel.showDeleteConfirmation() }
        )
    } else if (viewModel.selectedFiles.size > 1) {
        RemoveButton(
            selectedSize = viewModel.getSelectedFilesSize(),
            onClick = { viewModel.showDeleteConfirmation() }
        )
    }
}

/**
 * Bottom bar for media browser operations
 */
@Composable
fun MediaBrowserBottomBar(viewModel: MediaBrowserViewModel) {
    val context = LocalContext.current

    if (viewModel.selectedFiles.size == 1) {
        FileOperationBar(
            onDetailClick = { viewModel.showFileDetails() },
            onOpenClick = {
                viewModel.currentSelectedFile?.let { file ->
                    FileUtils.openFile(context, file.file)
                }
            },
            onShareClick = {
                viewModel.currentSelectedFile?.let { file ->
                    FileUtils.shareFile(context, file.file)
                }
            },
            onRenameClick = { viewModel.showRenameFile() },
            onRemoveClick = { viewModel.showDeleteConfirmation() }
        )
    } else if (viewModel.selectedFiles.size > 1) {
        RemoveButton(
            selectedSize = viewModel.getSelectedFilesSize(),
            onClick = { viewModel.showDeleteConfirmation() }
        )
    }
}