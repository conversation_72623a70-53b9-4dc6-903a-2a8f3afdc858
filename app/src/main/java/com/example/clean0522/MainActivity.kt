package com.example.clean0522

import android.Manifest
import android.annotation.SuppressLint
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.activity.compose.BackHandler
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.clean0522.data.manager.AntivirusPreferencesManager
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.data.repository.StorageRepositoryImpl
import com.example.clean0522.domain.usecase.GetStorageInfoUseCase
import com.example.clean0522.ui.components.AntivirusPrivacyDialog
import com.example.clean0522.ui.components.ExitDialog
import com.example.clean0522.ui.features.files.FilesViewModel
import com.example.clean0522.ui.navigation.BaseHomeNavigation
import com.example.clean0522.ui.navigation.BottomNavBar
import com.example.clean0522.ui.navigation.Screen
import com.example.clean0522.ui.navigation.TopNavBar
import com.example.clean0522.ui.navigation.backNavigation
import com.example.clean0522.ui.theme.Clean0522Theme
import com.example.clean0522.utils.PermissionUtils
import com.ext.adfusion.AdFusionController

class MainActivity : ComponentBaseActivity() {

    @SuppressLint("ViewModelConstructorInComposable")
    @Composable
    override fun setHomePage() {
        val storageRepository = StorageRepositoryImpl(this)
        val getStorageInfoUseCase = GetStorageInfoUseCase(storageRepository)

        // Get navigate_to parameter from intent
        val navigateTo = intent.getStringExtra("navigate_to")

        Clean0522Theme {
            val filesViewModel = FilesViewModel(getStorageInfoUseCase)
            MainScreen(
                filesViewModel = filesViewModel,
                activity = this@MainActivity,
                initialDestination = navigateTo
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    filesViewModel: FilesViewModel,
    activity: ComponentBaseActivity,
    initialDestination: String? = null
) {
    val context = LocalContext.current
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // Set initial destination if provided
    LaunchedEffect(initialDestination) {
        initialDestination?.let { destination ->
            when (destination) {
                "security_scan" -> {
                    if (currentRoute != Screen.Antivirus.route) {
                        backNavigation(navController, Screen.Antivirus.route)
                    }
                }
                else -> {activity.navigateToFeature(destination)}
            }
        }
    }

    // Antivirus privacy dialog state
    var showAntivirusPrivacyDialog by remember { mutableStateOf(false) }
    var pendingScanType by remember { mutableStateOf<String?>(null) }
    val antivirusPreferencesManager = remember { AntivirusPreferencesManager.getInstance() }
    var showBackDialog by remember { mutableStateOf(false) }
    var showGuildScreen by remember { mutableStateOf(!ComPreferencesManager.instance.isShowGuide()) }

    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = {
            showGuildScreen = false
        }
    )

    val showBottomBar = currentRoute in listOf(
        Screen.Files.route,
        Screen.Antivirus.route,
        Screen.More.route
    )

    val title = when (currentRoute) {
        Screen.Files.route -> stringResource(R.string.nav_files)
        Screen.Antivirus.route -> stringResource(R.string.nav_antivirus)
        Screen.More.route -> stringResource(R.string.nav_more)
        Screen.Settings.route -> stringResource(R.string.nav_settings)
        else -> stringResource(R.string.app_name)
    }

    val showBackButton = currentRoute == Screen.Settings.route

    val showSettingsButton = currentRoute in listOf(
        Screen.Files.route,
        Screen.Antivirus.route,
        Screen.More.route
    )

    Scaffold(
        containerColor = if (showGuildScreen) Color.White else colorResource(R.color.bg_color),
        modifier = Modifier.navigationBarsPadding(),
        topBar = {
            TopNavBar(
                title = title,
                showBackButton = showBackButton,
                backButtonAction = { navController.popBackStack() },
                settingsButtonContent = {
                    if (showSettingsButton) {
                        Image(
                            painter = painterResource(if (currentRoute == Screen.Antivirus.route){
                                R.mipmap.ic_info
                            }else{
                                R.mipmap.home_set
                            }),
                            contentDescription = stringResource(R.string.settings),
                            modifier = Modifier.size(28.dp)
                                .clickable {
                                    if (currentRoute == Screen.Antivirus.route) {
                                        showAntivirusPrivacyDialog = true
                                    } else {
                                        navController.navigate(Screen.Settings.route)
                                    }
                                }
                        )
                    }
                }
            )
        },
        bottomBar = {
            Column(modifier = Modifier.fillMaxWidth()) {
                if (!showGuildScreen){
                    AdFusionController.getBannerAd().BannerAdView()
                    if (showBottomBar) {
                        Card(elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                            shape = RectangleShape
                        ) {
                            BottomNavBar(navController = navController)
                        }
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (showGuildScreen && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && !PermissionUtils.hasNotificationPermission(context)){
                NotificationGuide(permissionLauncher)
            }else{
                BaseHomeNavigation(
                    navController = navController,
                    filesViewModel = filesViewModel,
                    activity = activity,
                    onRequestAntivirusPrivacyDialog = { scanType ->
                        pendingScanType = scanType
                        showAntivirusPrivacyDialog = true
                    }
                )
            }
        }
    }

    // Antivirus privacy dialog
    if (showAntivirusPrivacyDialog) {
        AntivirusPrivacyDialog(
            onDismiss = {
                showAntivirusPrivacyDialog = false
                pendingScanType = null
            },
            onConfirm = {
                antivirusPreferencesManager.setPrivacyAgreementAccepted(true)
                showAntivirusPrivacyDialog = false

                pendingScanType?.let { scanType ->
                    activity.checkStoragePermissionAndExecute {
                        activity.showRewardedAdAndExecute {
                            val intent = AntivirusScanActivity.createIntent(context, scanType)
                            context.startActivity(intent)
                        }
                    }
                }
                pendingScanType = null
            }
        )
    }

    if (showBackDialog){
        ExitDialog(
            onDismiss = { showBackDialog = false },
            onAllow = {
                showBackDialog = false
                Handler(Looper.getMainLooper()).postDelayed({
                    activity.moveTaskToBack(true)
                }, 200)
            }
        )
    }

    BackHandler {
        if (currentRoute == Screen.Settings.route) {
            navController.popBackStack()
        } else {
            showBackDialog = true
        }
    }
}

@Composable
fun NotificationGuide(permissionLauncher: ManagedActivityResultLauncher<String, Boolean>) {
    Column(horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(16.dp)) {
        Image(painter = painterResource(R.drawable.img_noti_guide),
            contentDescription = null,
            modifier = Modifier.width(160.dp)
        )

        Spacer(modifier = Modifier.height(32.dp))


        Text(text = stringResource(R.string.notification_permission_request_title),
            fontSize = 23.sp,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.SemiBold,
            color = colorResource(R.color.text_black)
        )
        Spacer(modifier = Modifier.height(32.dp))
        Image(painter = painterResource(R.drawable.img_noti_present),
            contentDescription = null,
            modifier = Modifier.width(300.dp)
        )
        Spacer(modifier = Modifier.height(32.dp))
        Text(text = stringResource(R.string.notification_settings_message),
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            color = colorResource(R.color.text_gray_70),
            fontWeight = FontWeight.SemiBold
            )

        Spacer(modifier = Modifier.weight(1f))

        Button(
            onClick = {
                permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                NotificationPreferencesManager.getInstance().setNotificationCount()
                NotificationPreferencesManager.getInstance().setLastNotificationTime(System.currentTimeMillis())
                ComPreferencesManager.instance.setShowGuide(true)
            },
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(R.color.text_blue)
            ),
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = 40.dp)
        ) {
            Text(stringResource(R.string.next),
                color = Color.White,
                fontSize = 13.sp,
                fontWeight = FontWeight.Medium
            )
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}
