package com.example.clean0522

import android.app.Activity
import android.app.Application
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.collection.arraySetOf
import com.example.clean0522.CleanApp.Companion.isAppInBackground
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.notify.BarServiceManager
import com.example.clean0522.notify.NotificationShowManage
import com.example.clean0522.notify.TipsShowManager
import com.example.clean0522.receiver.TickRingReceiver
import com.example.clean0522.receiver.TipShowReceiver
import com.example.clean0522.utils.FileUtils
import com.example.clean0522.utils.logD
import com.ext.FlyerUtil
import com.ext.adfusion.AdFusionController
import com.ext.firbase.FBAppUtil
import com.ext.firbase.UpdateEvent
import com.ext.remoteset.AnalyticsManager
import com.ext.remoteset.ConfigManager
import com.ext.remoteset.ConfigParameters
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * Application class for initialization
 */
class CleanApp : Application() {
    companion object{
        lateinit var appContext: Context
        var isAppInBackground = false
    }

    override fun onCreate() {
        super.onCreate()
        appContext = this
        registerActivityLifecycleCallbacks(GlobalLifecycleObserver())
        MMKV.initialize(this)

        UpdateEvent.event("app_start")
        BarServiceManager.startService(this)
        if (ComPreferencesManager.instance.getRegisterTime() == 0L){
            ComPreferencesManager.instance.setRegisterTime(System.currentTimeMillis())
            ConfigManager.configStartGet()
        }else{
            ConfigManager.newConfigFetchRequest()
        }

        FBAppUtil.sdkFirebaseAppInit(this)
        FlyerUtil.initAppsflyer(this)

        TickRingReceiver().registerReceiver(this)
        TipShowReceiver().registerReceiver(this)

        if (ComPreferencesManager.instance.isTermsAccepted()){
            GlobalScope.launch {
                AdFusionController.initAdSdk(appContext, false)
            }
        }

        val a = FileUtils.parseJsonFromAssets("config.json", ConfigParameters::class.java)
        logD("liyq", "data: $a")
    }
}

class GlobalLifecycleObserver : Application.ActivityLifecycleCallbacks {
    private var runningActivities = 0

    override fun onActivityCreated(p0: Activity, p1: Bundle?) {
        logD("GlobalLifecycleObserver", "onActivityCreated $p0")
        val tipType = p0.intent.getStringExtra(NotificationShowManage.EXTRA_TIP_TYPE) ?: ""
        if (tipType.isNotBlank()){
            tipClickEvent(tipType)
            TipsShowManager.closeHandel()
            val channel = p0.intent.getIntExtra(NotificationShowManage.EXTRA_CHANNEL, 0)
            try {
                val manager = CleanApp.appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                manager.cancel(channel)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onActivityStarted(p0: Activity) {
        logD("GlobalLifecycleObserver", "onActivityStarted $p0")
        runningActivities++
        if (runningActivities == 0 && isAppInBackground && !excludePage(p0)) {
            isAppInBackground = false
            val intent = Intent(CleanApp.appContext, SplashActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra("splash_enter",true)
            }
            CleanApp.appContext.startActivity(intent)
        }
        runningActivities++
    }

    override fun onActivityResumed(p0: Activity) {
        logD("GlobalLifecycleObserver", "onActivityResumed $p0")
    }

    override fun onActivityPaused(p0: Activity) {
        logD("GlobalLifecycleObserver", "onActivityPaused $p0")
    }

    override fun onActivityStopped(p0: Activity) {
        logD("GlobalLifecycleObserver", "onActivityStopped $p0")
        runningActivities--
        if (runningActivities == 0) {
            isAppInBackground = true
        }
    }

    override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
        logD("GlobalLifecycleObserver", "onActivitySaveInstanceState $p0")
    }

    override fun onActivityDestroyed(p0: Activity) {
        logD("GlobalLifecycleObserver", "onActivityDestroyed $p0")
    }

}

private fun tipClickEvent(type: String){
    UpdateEvent.event("tip_click_all")
    AnalyticsManager.sendCustomEvent("click_post", arraySetOf())
    when(type){
        "RAM" -> {
            AnalyticsManager.sendTipClick("click_ram")
            UpdateEvent.event("tip_click_ram")
        }
        "Battery" -> {
            AnalyticsManager.sendTipClick("click_battery")
            UpdateEvent.event("tip_click_battery")
        }
        "Storage" -> {
            AnalyticsManager.sendTipClick("click_storage")
            UpdateEvent.event("tip_click_storage")
        }
        "Antivirus" -> {
            AnalyticsManager.sendTipClick("click_antivirus")
            UpdateEvent.event("tip_click_antivirus")
        }
        "Large" -> {
            AnalyticsManager.sendTipClick("click_large")
            UpdateEvent.event("tip_click_large")
        }
        "Recent" -> {
            AnalyticsManager.sendTipClick("click_recent")
            UpdateEvent.event("tip_click_recent")
        }
        "Redundant" -> {
            AnalyticsManager.sendTipClick("click_redundant")
            UpdateEvent.event("tip_click_redundant")
        }
        "App" -> {
            AnalyticsManager.sendTipClick("click_app")
            UpdateEvent.event("tip_click_app")
        }
        else -> {
            AnalyticsManager.sendTipClick("click_other")
            UpdateEvent.event("tip_click_other")
        }
    }
}

private fun excludePage(activity: Activity): Boolean {
    return activity is SplashActivity || !activity.componentName.className.startsWith(CleanApp.appContext.packageName,true)
}
