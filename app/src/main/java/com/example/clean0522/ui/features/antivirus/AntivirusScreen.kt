package com.example.clean0522.ui.features.antivirus

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.AntivirusScanActivity
import com.example.clean0522.ComponentBaseActivity
import com.example.clean0522.R
import com.example.clean0522.data.manager.AntivirusPreferencesManager
import com.example.clean0522.utils.PermissionUtils
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

/**
 * Antivirus screen composable
 */
@Composable
fun AntivirusScreen(
    activity: ComponentBaseActivity? = null,
    onRequestPrivacyDialog: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val preferencesManager = remember { AntivirusPreferencesManager.getInstance() }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .verticalScroll(rememberScrollState())
    ) {

        val baseMessage = stringResource(R.string.antivirus_subtitle)
        val highlightSuffix = stringResource(R.string.antivirus_powered)
        val fullText = baseMessage.format(highlightSuffix)

        val defaultTextStyle = SpanStyle(
            color = colorResource(R.color.text_black),
            fontSize = 13.sp,
            fontWeight = FontWeight.SemiBold
        )

        val annotatedString = buildAnnotatedString {
            val highlight = SpanStyle(
                color = Color(0xFFFFAB2F),
                fontSize = 13.sp,
                fontWeight = FontWeight.SemiBold
            )

            withStyle(defaultTextStyle) {
                val highlightStart = fullText.indexOf(highlightSuffix)
                var currentIndex = 0

                if (highlightStart > 0) {
                    append(fullText.substring(currentIndex, highlightStart))
                    currentIndex = highlightStart
                }

                if (highlightStart >= 0) {
                    withStyle(highlight) {
                        append(highlightSuffix)
                    }
                }

                if (highlightStart < fullText.length) {
                    append(fullText.substring(currentIndex + highlightSuffix.length))
                }
            }
        }

        Text(
            text = annotatedString,
            fontSize = 13.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
            fontWeight = FontWeight.SemiBold
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Large placeholder area
        Image(painter = painterResource(R.mipmap.img_anti),
            contentDescription = null
        )

        if (AdFusionConfig.openNative()){
            AdFusionController.getNativeAd().NativeAdView(showPadding = true)
        }

        // Scan options
        AntivirusScanOption(
            title = stringResource(R.string.antivirus_quick_scan),
            iconRes = R.mipmap.ic_quick,
            description = stringResource(R.string.antivirus_quick_scan_desc),
            onScanClick = {
                handleScanClick(
                    scanType = AntivirusScanActivity.SCAN_TYPE_QUICK,
                    preferencesManager = preferencesManager,
                    context = context,
                    activity = activity,
                    onRequestPrivacyDialog = onRequestPrivacyDialog
                )
            }
        )

        Spacer(modifier = Modifier.height(16.dp))

        AntivirusScanOption(
            title = stringResource(R.string.antivirus_complete_scan),
            iconRes = R.mipmap.ic_comp,
            description = stringResource(R.string.antivirus_complete_scan_desc),
            onScanClick = {
                handleScanClick(
                    scanType = AntivirusScanActivity.SCAN_TYPE_COMPLETE,
                    preferencesManager = preferencesManager,
                    context = context,
                    activity = activity,
                    onRequestPrivacyDialog = onRequestPrivacyDialog
                )
            }
        )
        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * Individual scan option component
 */
@Composable
fun AntivirusScanOption(
    title: String,
    iconRes: Int,
    description: String,
    onScanClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon placeholder
            Image(
                painter = painterResource(iconRes),
                contentDescription = null,
                modifier = Modifier.size(88.dp)
            )

            Spacer(modifier = Modifier.width(10.dp))

            // Text content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.text_black)
                )

                Text(
                    text = description,
                    fontSize = 12.sp,
                    minLines = 3,
                    lineHeight = 16.sp,
                    color = Color(0xFF707070)
                )

                Spacer(modifier = Modifier.height(6.dp))

                Button(
                    onClick = onScanClick,
                    shape = RoundedCornerShape(20.dp),
                    modifier = Modifier
                        .height(35.dp)
                        .fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF4F7DFF)
                    )
                ) {
                    Text(
                        text = stringResource(R.string.antivirus_scan_now),
                        fontSize = 14.sp,
                        color = Color.White
                    )
                }
            }

        }
    }
}

/**
 * Handle scan click logic with privacy agreement and storage permission checks
 */
private fun handleScanClick(
    scanType: String,
    preferencesManager: AntivirusPreferencesManager,
    context: android.content.Context,
    activity: ComponentBaseActivity?,
    onRequestPrivacyDialog: (String) -> Unit
) {
    val hasPrivacyAgreement = preferencesManager.isPrivacyAgreementAccepted()
    val hasStoragePermission = PermissionUtils.hasStoragePermission(context)

    when {
        // Both conditions met - show rewarded ad dialog
        hasPrivacyAgreement && hasStoragePermission -> {
            activity?.showRewardedAdAndExecute {
                val intent = AntivirusScanActivity.createIntent(context, scanType)
                context.startActivity(intent)
            }
        }

        // No privacy agreement - show privacy dialog first
        !hasPrivacyAgreement -> {
            onRequestPrivacyDialog(scanType)
        }

        // Has privacy agreement but no storage permission - check permission
        hasPrivacyAgreement && !hasStoragePermission -> {
            activity?.checkStoragePermissionAndExecute {
                // After storage permission, show rewarded ad dialog
                activity.showRewardedAdAndExecute {
                    val intent = AntivirusScanActivity.createIntent(context, scanType)
                    context.startActivity(intent)
                }
            }
        }
    }
}
