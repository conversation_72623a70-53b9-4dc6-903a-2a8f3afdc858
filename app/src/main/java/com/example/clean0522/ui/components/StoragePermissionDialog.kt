package com.example.clean0522.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.clean0522.R

/**
 * Storage permission dialog component
 */
@Composable
fun StoragePermissionDialog(
    onDismiss: () -> Unit,
    onAllow: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                tint = Color(0xFF7E7E7E),
                modifier = Modifier.size(32.dp)
                    .align(Alignment.End)
                    .background(Color.White, CircleShape)
                    .clickable { onDismiss() }
                    .padding(6.dp)
            )


            Spacer(modifier = Modifier.height(16.dp))

            Column(modifier = Modifier
                .background(Color.White, RoundedCornerShape(20.dp))
                .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally) {

                Text(
                    text = stringResource(R.string.storage_permission_title),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    color = colorResource(R.color.text_black)
                )

                Spacer(modifier = Modifier.height(18.dp))

                // Message
                Text(
                    text = stringResource(R.string.storage_permission_message),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    color = Color(0xFF8F8F8F),
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Allow button
                Button(
                    onClick = onAllow,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.text_blue)
                    ),
                    shape = RoundedCornerShape(10.dp)
                ) {
                    Text(
                        text = stringResource(R.string.allow),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }
    }
}

/**
 * Notification permission dialog component
 */
@Composable
fun NotificationPermissionDialog(
    onDismiss: () -> Unit,
    onAllow: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                tint = Color(0xFF7E7E7E),
                modifier = Modifier.size(32.dp)
                    .align(Alignment.End)
                    .background(Color.White, CircleShape)
                    .clickable { onDismiss() }
                    .padding(6.dp)
            )


            Spacer(modifier = Modifier.height(16.dp))

            Column(modifier = Modifier
                .background(Color.White, RoundedCornerShape(20.dp))
                .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally) {

                Text(
                    text = stringResource(R.string.notification_permission_title),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    color = colorResource(R.color.text_black)
                )

                Spacer(modifier = Modifier.height(18.dp))

                // Message
                Text(
                    text = stringResource(R.string.notification_permission_message),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    color = Color(0xFF8F8F8F),
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Allow button
                Button(
                    onClick = onAllow,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.text_blue)
                    ),
                    shape = RoundedCornerShape(10.dp)
                ) {
                    Text(
                        text = stringResource(R.string.allow),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }
    }
}

@Composable
fun ExitDialog(
    onDismiss: () -> Unit,
    onAllow: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                tint = Color(0xFF7E7E7E),
                modifier = Modifier.size(32.dp)
                    .align(Alignment.End)
                    .background(Color.White, CircleShape)
                    .clickable { onDismiss() }
                    .padding(6.dp)
            )


            Spacer(modifier = Modifier.height(16.dp))

            Column(modifier = Modifier
                .background(Color.White, RoundedCornerShape(20.dp))
                .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally) {

                Text(
                    text = stringResource(R.string.exit_app),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center,
                    color = colorResource(R.color.text_black)
                )

                Spacer(modifier = Modifier.height(18.dp))

                // Message
                Text(
                    text = stringResource(R.string.exit_message),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    color = Color(0xFF8F8F8F),
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.text_gray)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.cancel),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.text_black)
                        )
                    }

                    Button(
                        onClick = onAllow,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.text_blue)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.confirm),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}
