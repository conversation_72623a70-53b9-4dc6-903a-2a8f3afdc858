package com.example.clean0522.ui.features.more

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.AppInfoActivity
import com.example.clean0522.ComponentBaseActivity
import com.example.clean0522.FileUtilityActivity
import com.example.clean0522.R
import com.example.clean0522.SystemInfoActivity
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

/**
 * More screen composable showing system tools
 */
@Composable
fun MoreScreen(
    activity: ComponentBaseActivity? = null
) {
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        if (AdFusionConfig.openNative()){
            AdFusionController.getNativeAd().NativeAdView()
        }
        val toolGroups = getToolItems().chunked(3)

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            toolGroups.forEach { group ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    group.forEach { tool ->
                        ToolItem(
                            tool = tool,
                            onClick = { handleToolClick(context, tool.type, activity) },
                            modifier = Modifier.weight(1f)
                        )
                    }

                    repeat(3 - group.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

/**
 * Individual tool item composable
 */
@Composable
fun ToolItem(
    tool: ToolItemData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 10.dp)
            .height(100.dp)
            .clickable { onClick() },
    ){
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(65.dp)
                .align(Alignment.BottomCenter)
                .clickable { onClick() },
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {}
        Column(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            Image(
                painter = painterResource(id = tool.iconRes),
                contentDescription = stringResource(id = tool.titleRes),
                modifier = Modifier.size(64.dp)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = stringResource(id = tool.titleRes),
                fontSize = 12.sp,
                minLines = 2,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = colorResource(R.color.text_black),
                lineHeight = 16.sp
            )
        }
    }
}

/**
 * Data class for tool items
 */
data class ToolItemData(
    val type: String,
    val titleRes: Int,
    val iconRes: Int
)

/**
 * Get list of tool items
 */
fun getToolItems(): List<ToolItemData> {
    return listOf(
        ToolItemData(
            type = "device_info",
            titleRes = R.string.tool_device_info,
            iconRes = R.mipmap.tool_device
        ),
        ToolItemData(
            type = "ram_usage",
            titleRes = R.string.tool_ram_usage,
            iconRes = R.mipmap.tool_ram
        ),
        ToolItemData(
            type = "battery_info",
            titleRes = R.string.tool_battery_info,
            iconRes = R.mipmap.tool_battery
        ),
        ToolItemData(
            type = "cpu_monitor",
            titleRes = R.string.tool_cpu_monitor,
            iconRes = R.mipmap.tool_cpu
        ),
        ToolItemData(
            type = "app_manager",
            titleRes = R.string.tool_app_manager,
            iconRes = R.mipmap.tool_app_ma
        ),
        ToolItemData(
            type = "app_process",
            titleRes = R.string.tool_app_process,
            iconRes = R.mipmap.tool_app_mo
        ),
        ToolItemData(
            type = "network",
            titleRes = R.string.tool_network,
            iconRes = R.mipmap.tool_network
        ),
        ToolItemData(
            type = "similar_photos",
            titleRes = R.string.tool_similar_photos,
            iconRes = R.mipmap.tool_similar
        )
    )
}

/**
 * Handle tool item click
 */
fun handleToolClick(context: Context, toolType: String, activity: ComponentBaseActivity? = null) {
    when (toolType) {
        "similar_photos" -> {
            // Navigate to existing Similar Photos feature with permission check
            if (activity != null) {
                activity.checkStoragePermissionAndExecute {
                    val intent = Intent(context, FileUtilityActivity::class.java).apply {
                        putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, FileUtilityActivity.TYPE_SIMILAR_PHOTOS)
                    }
                    context.startActivity(intent)
                }
            } else {
                val intent = Intent(context, FileUtilityActivity::class.java).apply {
                    putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, FileUtilityActivity.TYPE_SIMILAR_PHOTOS)
                }
                context.startActivity(intent)
            }
        }
        "device_info" -> {
            // Navigate to SystemInfoActivity with device info
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_DEVICE_INFO)
            }
            context.startActivity(intent)
        }
        "ram_usage" -> {
            // Navigate to SystemInfoActivity with RAM usage
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_RAM_USAGE)
            }
            context.startActivity(intent)
        }
        "battery_info" -> {
            // Navigate to SystemInfoActivity with battery info
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_BATTERY_INFO)
            }
            context.startActivity(intent)
        }
        "cpu_monitor" -> {
            // Navigate to SystemInfoActivity with CPU monitor
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_CPU_MONITOR)
            }
            context.startActivity(intent)
        }
        "app_manager" -> {
            // Navigate to AppInfoActivity with app manager
            val intent = Intent(context, AppInfoActivity::class.java).apply {
                putExtra(AppInfoActivity.EXTRA_APP_TYPE, AppInfoActivity.TYPE_APP_MANAGER)
            }
            context.startActivity(intent)
        }
        "app_process" -> {
            // Navigate to AppInfoActivity with app process
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_APP_PROCESS)
            }
            context.startActivity(intent)
        }
        "network" -> {
            // Navigate to SystemInfoActivity with network info
            val intent = Intent(context, SystemInfoActivity::class.java).apply {
                putExtra(SystemInfoActivity.EXTRA_INFO_TYPE, SystemInfoActivity.TYPE_NETWORK)
            }
            context.startActivity(intent)
        }
    }
}
