package com.example.clean0522.ui.components

import android.app.Activity
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.clean0522.R
import com.example.clean0522.data.manager.ComPreferencesManager
import com.example.clean0522.ui.features.settings.sentEmail
import com.example.clean0522.utils.logD
import com.ext.firbase.UpdateEvent
import com.google.android.play.core.review.testing.FakeReviewManager
import kotlin.math.abs

@Composable
@Preview
fun ScoreDialog(
    onDismissRequest: () -> Unit = {}
) {
    val context = LocalContext.current
    var currentRate by remember { mutableStateOf(0) }
    var showFeedbackButton by remember { mutableStateOf(false) }

    LaunchedEffect(Unit){
        ComPreferencesManager.instance.setScoreLastTime(System.currentTimeMillis())
        ComPreferencesManager.instance.setScoreCount()
    }

    Dialog(onDismissRequest = {}) {
        Box(contentAlignment = Alignment.TopCenter){
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 50.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color.White)
                    .padding(24.dp)
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.icon_close),
                    contentDescription = "Close",
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(24.dp)
                        .offset(y = (-10).dp)
                        .clickable { onDismissRequest() }
                )

                Column(modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(30.dp))

                    if (showFeedbackButton || currentRate == 0) {
                        val title = if (currentRate == 0){
                            stringResource(R.string.rate_title)
                        }else {
                            stringResource(R.string.rate_feedback_title)
                        }
                        Text(
                            text = title,
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_black),
                            fontWeight = FontWeight.Medium,
                            textAlign = TextAlign.Center
                        )
                    } else {
                        val titleParts = stringResource(R.string.rate_helpful_title).split("%s")

                        Text(
                            buildAnnotatedString {
                                withStyle(style = SpanStyle(color = colorResource(R.color.text_black))) {
                                    append(titleParts[0])
                                }
                                withStyle(style = SpanStyle(color = colorResource(R.color.text_blue))) {
                                    append(stringResource(R.string.app_name))
                                }
                                withStyle(style = SpanStyle(color = colorResource(R.color.text_black))) {
                                    if (titleParts.size > 1) append(titleParts[1])
                                }
                            }
                        )
                    }

                    Spacer(modifier = Modifier.height(30.dp))

                    Box{
                        if (showFeedbackButton) {
                            Button(
                                onClick = {
                                    sentEmail(context)
                                    onDismissRequest()
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(48.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = colorResource(R.color.text_blue)
                                ),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.rate_send_back),
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.SemiBold,
                                    color = Color.White
                                )
                            }
                        }else{
                            if (currentRate == 0) {
                                RateScoreScreen(
                                    selectedRating = currentRate,
                                    onRatingSelected = { rating ->
                                        currentRate = rating
                                        ComPreferencesManager.instance.setRateShow(true)
                                    }
                                )
                            } else if (currentRate <= 4) {
                                RequestScreen(
                                    onDismissRequest = onDismissRequest,
                                    onFeedback = {
                                        showFeedbackButton = true
                                    }
                                )
                            } else {
                                showGpRateView(context)
                                onDismissRequest()
                            }
                        }
                    }
                }
            }

            val img = if (showFeedbackButton)
                R.mipmap.rate_feedback
            else {
                if (currentRate == 0){
                    R.mipmap.img_rate
                }else{
                    R.mipmap.rate_help
                }
            }


            Image(painterResource(img),
                contentDescription = "Rate",
                modifier = Modifier.size(105.dp)
            )
        }
    }
}

@Composable
private fun RateScoreScreen(
    selectedRating: Int = 0,
    onRatingSelected: (Int) -> Unit = {},
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        RateScoreIcon(
            currentRating = selectedRating,
            onRatingChanged = onRatingSelected
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun RequestScreen(
    onDismissRequest: () -> Unit,
    onFeedback: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Button(
            onClick = onFeedback,
            modifier = Modifier
                .weight(1f)
                .height(48.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(R.color.text_gray)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = stringResource(R.string.rate_not_really),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_black)
            )
        }

        Button(
            onClick = onDismissRequest,
            modifier = Modifier
                .weight(1f)
                .height(48.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(R.color.text_blue)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = stringResource(R.string.rate_helpful),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color.White
            )
        }
    }
}

@Composable
private fun RateScoreIcon(
    currentRating: Int,
    onRatingChanged: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        for (i in 1..5) {
            val icon = if (i <= currentRating) R.mipmap.ck_check else R.mipmap.ck_en
            Image(
                painter = painterResource(id = icon),
                contentDescription = "score $i",
                modifier = Modifier
                    .size(44.dp)
                    .clickable { onRatingChanged(i) }
                    .padding(2.dp)
            )

            if (i < 5) {
                Spacer(modifier = Modifier.width(2.dp))
            }
        }
    }
}

private fun showGpRateView(context: Context) {
    try {
        UpdateEvent.event("review_request")
//        val manager = ReviewManagerFactory.create(context)
        val manager = FakeReviewManager(context)
        manager.requestReviewFlow().apply {
            addOnCompleteListener {
                if (it.isSuccessful){
                    val reviewInfo = it.result
                    val flow = manager.launchReviewFlow(context as Activity,reviewInfo)
                    flow.addOnCompleteListener { task ->
                        if (task.isSuccessful){
                            logD("RateView","GPReview Success")
                            UpdateEvent.event("review_success")
                        }else{
                            logD("RateView","GPReview Error: ${task.exception?.message}")
                            UpdateEvent.event("review_error")
                        }
                    }
                }
            }
        }
    } catch (_: Exception) {
        UpdateEvent.event("review_error_1")
    }
}

fun checkAllowShowScoreDialog(): Boolean{
    if(ComPreferencesManager.instance.isRateShow()) return false
    val showCount = ComPreferencesManager.instance.getScoreCount()
    if (showCount > 3) return false

    val lastTime = ComPreferencesManager.instance.getScoreLastTime()
    val interval = abs(System.currentTimeMillis() - lastTime)
    return when(showCount){
        0 -> true
        1 -> interval >= 3 * 24 * 60 * 60 * 1000
        2 -> interval >= 5 * 24 * 60 * 60 * 1000
        else -> false
    }

}