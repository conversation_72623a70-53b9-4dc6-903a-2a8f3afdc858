package com.example.clean0522.ui.features.antivirus

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import com.example.clean0522.R
import com.example.clean0522.domain.model.ScanResult
import com.example.clean0522.domain.model.ThreatInfo
import com.example.clean0522.domain.model.ThreatLevel
import com.ext.adfusion.AdFusionController

/**
 * Screen showing scan results
 */
@Composable
fun ScanResultScreen(
    scanResult: ScanResult,
    onIgnoreThreat: (ThreatInfo) -> Unit,
    onDeleteThreat: (ThreatInfo) -> Unit,
    onNavigateToFeature: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val featureRecommendations = getFeatureRecommendations()
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            val baseMessage = stringResource(R.string.antivirus_result_sub)
            val highlightSuffix = stringResource(R.string.antivirus_provided)
            val fullText = baseMessage.format(highlightSuffix)

            val defaultTextStyle = SpanStyle(
                color = colorResource(R.color.text_black),
                fontSize = 13.sp,
                fontWeight = FontWeight.SemiBold
            )

            val annotatedString = buildAnnotatedString {
                val highlight = SpanStyle(
                    color = Color(0xFFFFAB2F),
                    fontSize = 13.sp,
                    fontWeight = FontWeight.SemiBold
                )

                withStyle(defaultTextStyle) {
                    val highlightStart = fullText.indexOf(highlightSuffix)
                    var currentIndex = 0

                    if (highlightStart > 0) {
                        append(fullText.substring(currentIndex, highlightStart))
                        currentIndex = highlightStart
                    }

                    if (highlightStart >= 0) {
                        withStyle(highlight) {
                            append(highlightSuffix)
                        }
                    }

                    if (highlightStart < fullText.length) {
                        append(fullText.substring(currentIndex + highlightSuffix.length))
                    }
                }
            }

            Text(
                text = annotatedString,
                fontSize = 13.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.SemiBold
            )
        }

        item {
            ScanSummaryCard(
                totalScanned = scanResult.totalScanned,
                threatsFound = scanResult.threatCount
            )
        }

        item {
            AdFusionController.getNativeAd().NativeAdView()
        }

        if (scanResult.threatsFound.isNotEmpty()) {
            item {
                Text(
                    text = stringResource(R.string.delete_or_ignore_threats),
                    fontSize = 14.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(R.color.text_gray_70),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            items(scanResult.threatsFound) { threat ->
                ThreatItemCard(
                    threat = threat,
                    onIgnoreClick = { onIgnoreThreat(threat) },
                    onDeleteClick = { onDeleteThreat(threat) }
                )
            }
        } else {

            // Feature recommendations when no threats found
            item {
                Text(
                    text = stringResource(R.string.here_are_some_other_features),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            }

            items(featureRecommendations) { feature ->
                FeatureRecommendationCard(
                    feature = feature,
                    onFeatureClick = { onNavigateToFeature(feature.id) }
                )
            }
        }
    }
}

/**
 * Card showing scan summary
 */
@Composable
private fun ScanSummaryCard(
    totalScanned: Int,
    threatsFound: Int,
    modifier: Modifier = Modifier
) {

    Row (modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {

        Image(painter = painterResource(if (threatsFound > 0) R.mipmap.img_err else R.mipmap.img_comp),
            contentDescription = null,
            modifier = Modifier.size(95.dp))

        Spacer(modifier = Modifier.width(32.dp))

        Column {
            Text(
                text = stringResource(R.string.items_scanned, totalScanned),
                fontSize = 16.sp,
                color = colorResource(R.color.text_gray_70)
            )

            if (threatsFound == 0) {
                Text(
                    text = stringResource(R.string.no_threats_found),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 8.dp)
                ) {
                    Text(
                        text = "$threatsFound",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red
                    )
                    Text(
                        text = " " + stringResource(R.string.threats_found).replace("%1\$d", "").trim(),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }
        }
    }
}

/**
 * Card for individual threat item
 */
@Composable
private fun ThreatItemCard(
    threat: ThreatInfo,
    onIgnoreClick: () -> Unit,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(color = Color.White,RoundedCornerShape(12.dp))
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (threat.isInstalledApp && threat.packageName != null) {
                AppIconImage(
                    packageName = threat.packageName,
                    modifier = Modifier.size(50.dp)
                )
            } else {
                Image(
                    painter = painterResource(R.mipmap.type_file),
                    contentDescription = null,
                    modifier = Modifier.size(50.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = threat.displayName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.text_black)
                    )

                    Text(
                        text = if (threat.isInstalledApp) threat.displayPath else threat.filePath!!,
                        fontSize = 12.sp,
                        lineHeight = 14.sp,
                        maxLines = 2,
                        color = colorResource(R.color.text_gray_70),
                        fontWeight = FontWeight.SemiBold,
                    )

                    Text(
                        text = threat.virusName,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFFFF505E)
                    )
                }

                ThreatLevelBadge(threatLevel = threat.threatLevel)
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {

            Button(
                onClick = onIgnoreClick,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF8F9FAC)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    stringResource(R.string.ignore),
                    color = Color.White,
                    fontSize = 14.sp
                )
            }

            Button(
                onClick = onDeleteClick,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFF505E)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    stringResource(
                        if (threat.isInstalledApp) R.string.uninstall else R.string.delete
                    ),
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

/**
 * Badge showing threat level
 */
@Composable
fun ThreatLevelBadge(
    threatLevel: ThreatLevel
) {
    val (backgroundColor, textColor, text) = when (threatLevel) {
        ThreatLevel.MALWARE -> Triple(
            Color.Red.copy(alpha = 0.1f),
            Color.Red,
            stringResource(R.string.threat_level_malware)
        )
        ThreatLevel.PUA -> Triple(
            Color(0xFFFF9800).copy(alpha = 0.1f),
            Color(0xFFFF9800),
            stringResource(R.string.threat_level_pua)
        )
        ThreatLevel.SAFE -> Triple(
            Color.Green.copy(alpha = 0.1f),
            Color.Green,
            stringResource(R.string.threat_level_safe)
        )
    }

    Text(
        text = text,
        fontSize = 14.sp,
        fontWeight = FontWeight.SemiBold,
        color = textColor
    )
}


/**
 * Data class for feature recommendations
 */
data class FeatureRecommendation(
    val id: String,
    val title: String,
    val icon: Int
)

/**
 * Get list of feature recommendations
 */
@Composable
fun getFeatureRecommendations(): List<FeatureRecommendation> {
    return listOf(
        FeatureRecommendation(
            id = "device_storage",
            title = stringResource(R.string.device_storage),
            icon = R.mipmap.tool_storage
        ),
        FeatureRecommendation(
            id = "security_scan",
            title = stringResource(R.string.security_scan),
            icon = R.mipmap.tool_anti
        ),
        FeatureRecommendation(
            id = "recent_files",
            title = stringResource(R.string.utility_recent_files),
            icon = R.mipmap.ic_recent
        ),
        FeatureRecommendation(
            id = "large_files",
            title = stringResource(R.string.utility_large_files),
            icon = R.mipmap.ic_large
        ),
        FeatureRecommendation(
            id = "duplicate_files",
            title = stringResource(R.string.utility_duplicate_files),
            icon = R.mipmap.ic_duplicate
        ),
        FeatureRecommendation(
            id = "redundant_files",
            title = stringResource(R.string.utility_redundant_files),
            icon = R.mipmap.ic_redundant
        ),
        FeatureRecommendation(
            id = "ram_usage",
            title = stringResource(R.string.ram_usage),
            icon = R.mipmap.tool_ram
        ),
        FeatureRecommendation(
            id = "battery_info",
            title = stringResource(R.string.battery_info),
            icon = R.mipmap.tool_battery
        )
    )
}

@Composable
fun getFeatureTools(): List<FeatureRecommendation> {
    return listOf(
        FeatureRecommendation(
            id = "device_storage",
            title = stringResource(R.string.device_storage),
            icon = R.mipmap.tool_storage
        ),
        FeatureRecommendation(
            id = "security_scan",
            title = stringResource(R.string.security_scan),
            icon = R.mipmap.tool_anti
        ),
        FeatureRecommendation(
            id = "ram_usage",
            title = stringResource(R.string.ram_usage),
            icon = R.mipmap.tool_ram
        ),
        FeatureRecommendation(
            id = "battery_info",
            title = stringResource(R.string.battery_info),
            icon = R.mipmap.tool_battery
        )
    )
}

/**
 * Card for individual feature recommendation
 */
@Composable
fun FeatureRecommendationCard(
    feature: FeatureRecommendation,
    onFeatureClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 4.dp)
            .clickable { onFeatureClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Feature Icon
            Box(
                modifier = Modifier
                    .size(50.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(feature.icon),
                    contentDescription = feature.title,
                    modifier = Modifier.size(50.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // Feature Title
            Text(
                text = feature.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_black),
                modifier = Modifier.weight(1f)
            )

            Image(
                painter = painterResource(R.mipmap.ic_arrow),
                contentDescription = null,
                modifier = Modifier.size(25.dp)
            )
        }
    }
}

/**
 * Component to display app icon for installed apps
 */
@Composable
fun AppIconImage(
    packageName: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val appIcon = remember(packageName) {
        try {
            context.packageManager.getApplicationIcon(packageName)
        } catch (e: Exception) {
            null
        }
    }

    if (appIcon != null) {
        Image(
            painter = BitmapPainter(appIcon.toBitmap().asImageBitmap()),
            contentDescription = "App icon",
            modifier = modifier
                .size(24.dp)
                .clip(RoundedCornerShape(4.dp))
        )
    } else {
        // Fallback to default app icon
        Image(
            painter = painterResource(R.mipmap.ic_apk),
            contentDescription = "App icon",
            modifier = modifier
                .size(24.dp)
                .clip(RoundedCornerShape(4.dp))
        )
    }
}
