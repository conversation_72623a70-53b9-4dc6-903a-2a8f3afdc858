package com.example.clean0522.ui.features.antivirus

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R
import com.example.clean0522.domain.model.ThreatInfo


@Composable
fun IgnoreListScreen(
    ignoredThreats: List<ThreatInfo>,
    onUnignoreThreat: (ThreatInfo) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = stringResource(R.string.ignore_list_description),
                fontSize = 14.sp,
                color = colorResource(R.color.text_black),
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        // Ignored threats list
        if (ignoredThreats.isNotEmpty()) {
            items(ignoredThreats) { threat ->
                IgnoredThreatCard(
                    threat = threat,
                    onUnignoreClick = {
                        onUnignoreThreat(threat)
                    }
                )
            }
        } else {
            item {
                EmptyIgnoreListCard()
            }
        }
    }
}

/**
 * Card for individual ignored threat item
 */
@Composable
private fun IgnoredThreatCard(
    threat: ThreatInfo,
    onUnignoreClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(color = Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp),
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (threat.isInstalledApp && threat.packageName != null) {
                AppIconImage(
                    packageName = threat.packageName,
                    modifier = Modifier.size(50.dp)
                )
            } else {
                Image(
                    painter = painterResource(R.mipmap.type_file),
                    contentDescription = null,
                    modifier = Modifier.size(50.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = threat.displayName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier,
                        color = colorResource(R.color.text_black)
                    )

                    Text(
                        text = if (threat.isInstalledApp) threat.displayPath else threat.displayPath,
                        fontSize = 12.sp,
                        lineHeight = 14.sp,
                        maxLines = 2,
                        color = colorResource(R.color.text_gray_70),
                        modifier = Modifier
                    )

                    Text(
                        text = threat.virusName,
                        fontSize = 12.sp,
                        color = Color(0xFFFF505E)
                    )
                }

                ThreatLevelBadge(threatLevel = threat.threatLevel)
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Unignore Button
        Row(modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center) {
            Button(
                onClick = onUnignoreClick,
                modifier = Modifier.fillMaxWidth(0.5f)
                    .height(40.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF8F9FAC)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(text = stringResource(R.string.unignore),
                    color = Color.White,
                    fontSize = 14.sp)
            }
        }
    }

}


/**
 * Card shown when ignore list is empty
 */
@Composable
private fun EmptyIgnoreListCard(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {

        Spacer(modifier = Modifier.height(32.dp))

        Image(painter = painterResource(R.mipmap.empty_file),
            contentDescription = null,
            modifier = Modifier.size(120.dp))

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(R.string.no_ignored_threats),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
