package com.example.clean0522.ui.features.utility

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.clean0522.R
import com.example.clean0522.ResultActivity
import com.example.clean0522.ui.components.*
import com.example.clean0522.utils.FileUtils

/**
 * Redundant files content composable
 */
@Composable
fun RedundantFilesContent(viewModel: FileUtilityViewModel) {

    LaunchedEffect(Unit) {
        viewModel.loadRedundantFiles()
        viewModel.changeSelectMode(true)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (viewModel.redundantFileGroups.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(painter = painterResource(R.mipmap.empty_file),
                    contentDescription = null,
                    modifier = Modifier.size(150.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = stringResource(R.string.empty_folder),
                    fontSize = 14.sp,
                    color = colorResource(R.color.text_gray_70),
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f)
            ) {
                items(viewModel.redundantFileGroups) { group ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                        shape = RoundedCornerShape(15.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        )
                    ) {
                        FileGroupItem(
                            group = group,
                            isExpanded = viewModel.expandedGroups.contains(group.id),
                            isSelectionMode = viewModel.isSelectionMode,
                            isGroupSelected = viewModel.selectedGroups.contains(group.id),
                            selectedFiles = viewModel.selectedFiles,
                            onGroupClick = { viewModel.toggleGroupExpansion(group.id) },
                            onGroupLongClick = { /* No long click for redundant files */ },
                            onGroupSelectionToggle = { viewModel.toggleGroupSelection(group) },
                            onFileClick = { viewModel.toggleFileSelection(it) },
                            onFileLongClick = { /* No long click for redundant files */ },
                            onFileSelectionToggle = { viewModel.toggleFileSelection(it) },
                            showExpandIcon = true,
                            showGroupSelection = true
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                }
            }
        }
    }
    val context = LocalContext.current
    // Delete confirmation dialog
    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = {
                viewModel.deleteSelectedFiles()
                ResultActivity.startResultActivity(context, "redundant_files")
            }
        )
    }
}

/**
 * Similar photos content composable
 */
@Composable
fun SimilarPhotosContent(viewModel: FileUtilityViewModel) {
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadSimilarPhotos(context)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Photo groups list
        if (viewModel.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (viewModel.similarPhotoGroups.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(painter = painterResource(R.mipmap.empty_file),
                    contentDescription = null,
                    modifier = Modifier.size(150.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = stringResource(R.string.empty_folder),
                    fontSize = 14.sp,
                    color = colorResource(R.color.text_gray_70),
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f)
            ) {
                items(viewModel.similarPhotoGroups) { group ->
                    SimilarPhotosGroupItem(
                        group = group,
                        isSelectionMode = viewModel.isSelectionMode,
                        selectedFiles = viewModel.selectedFiles,
                        onFileClick = { viewModel.onFileClick(context,it) },
                        onFileLongClick = { viewModel.onFileLongClick(it) },
                        onFileSelectionToggle = { viewModel.toggleFileSelection(it) }
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }

    // Dialogs
    if (viewModel.showDetailsDialog && viewModel.currentSelectedFile != null) {
        FileDetailsDialog(
            fileItem = viewModel.currentSelectedFile!!,
            onDismiss = { viewModel.dismissFileDetails() }
        )
    }

    if (viewModel.showRenameDialog && viewModel.currentSelectedFile != null) {
        RenameFileDialog(
            currentName = viewModel.currentSelectedFile!!.name,
            onDismiss = { viewModel.dismissRenameFile() },
            onConfirm = { newName -> viewModel.renameFile(newName) }
        )
    }

    if (viewModel.showDeleteDialog) {
        DeleteConfirmationDialog(
            onDismiss = { viewModel.dismissDeleteConfirmation() },
            onConfirm = {
                viewModel.deleteSelectedFiles()
                ResultActivity.startResultActivity(context, "similar_photos")
            }
        )
    }
}
