package com.example.clean0522.ui.navigation

import android.util.Log
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.example.clean0522.R

/**
 * Bottom navigation bar composable
 */
@Composable
fun BottomNavBar(navController: NavController) {
    val items = listOf(
        BottomNavItem.Files,
        BottomNavItem.Antivirus,
        BottomNavItem.More
    )
    
    NavigationBar(
        containerColor = Color.White
    ) {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentRoute = navBackStackEntry?.destination?.route
        
        items.forEach { item ->
            val isSelected = currentRoute == item.route

            NavigationBarItem(
                icon = {
                    Icon(
                        painter = painterResource(
                            id = if (isSelected) item.selectedIcon else item.unselectedIcon
                        ),
                        contentDescription = stringResource(item.title),
                        tint = Color.Unspecified
                    )
                },
                label = {
                    Text(
                        text = stringResource(item.title),
                        color = colorResource(
                            id = if (isSelected) R.color.text_blue_select else R.color.text_gray_select
                        )
                    )
                },
                selected = isSelected,
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = Color.Unspecified,
                    unselectedIconColor = Color.Unspecified,
                    selectedTextColor = colorResource(R.color.text_blue_select),
                    unselectedTextColor = colorResource(R.color.text_gray_select),
                    indicatorColor = Color.Transparent // Remove selection indicator background
                ),
                onClick = {
                    if (currentRoute != item.route) {
                        backNavigation(navController,item.route)
                    }
                }
            )
        }
    }
}

fun backNavigation(navController: NavController,route: String,){
        navController.navigate(route) {
            popUpTo(navController.graph.startDestinationId) {
                saveState = true
            }
            launchSingleTop = true
            restoreState = true
        }
}

/**
 * Sealed class for bottom navigation items
 */
sealed class BottomNavItem(
    val route: String,
    val title: Int,
    val selectedIcon: Int,
    val unselectedIcon: Int
) {
    object Files : BottomNavItem(
        route = Screen.Files.route,
        title = R.string.nav_files,
        selectedIcon = R.mipmap.tab_files_s,
        unselectedIcon = R.mipmap.tab_files_n
    )

    object Antivirus : BottomNavItem(
        route = Screen.Antivirus.route,
        title = R.string.nav_antivirus,
        selectedIcon = R.mipmap.tab_antivirus_s,
        unselectedIcon = R.mipmap.tab_antivirus_n
    )

    object More : BottomNavItem(
        route = Screen.More.route,
        title = R.string.nav_more,
        selectedIcon = R.mipmap.tab_more_s,
        unselectedIcon = R.mipmap.tab_more_n
    )
}
