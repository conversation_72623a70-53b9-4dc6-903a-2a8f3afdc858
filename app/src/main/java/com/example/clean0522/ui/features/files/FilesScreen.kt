package com.example.clean0522.ui.features.files

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin
import com.example.clean0522.ComponentBaseActivity
import com.example.clean0522.FileBrowserActivity
import com.example.clean0522.FileUtilityActivity
import com.example.clean0522.R
import com.example.clean0522.ui.navigation.startFileBrowserActivity
import com.example.clean0522.ui.navigation.startFileBrowserActivityWithPermission
import com.example.clean0522.ui.navigation.startFileUtilityActivity
import com.example.clean0522.ui.navigation.startFileUtilityActivityWithPermission
import com.example.clean0522.ui.theme.ProgressBackgroundWhite
import com.example.clean0522.ui.theme.ProgressGradientEnd
import com.example.clean0522.ui.theme.ProgressGradientStart
import com.example.clean0522.ui.theme.ProgressShadowColor
import com.example.clean0522.ui.theme.StorageUsageBackgroundColor
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController

/**
 * Files screen composable
 */
@Composable
fun FilesScreen(
    viewModel: FilesViewModel,
    activity: ComponentBaseActivity? = null
) {
    val state by viewModel.state.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Storage usage visualization
        StorageUsageChart(
            usedPercentage = state.storageInfo.usagePercentage / 100f,
            usedSpace = state.formattedUsedSpace,
            totalSpace = state.formattedTotalSpace,
            isLoading = state.isLoading
        )

        Spacer(modifier = Modifier.height(12.dp))
        val context = LocalContext.current

        // Browse button
        Box(modifier = Modifier.fillMaxWidth()
            .padding(horizontal = 40.dp)
            .clickable {
                if (activity != null) {
                    startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_STORAGE)
                } else {
                    startFileBrowserActivity(context, FileBrowserActivity.TYPE_STORAGE)
                }
            }
            .background(brush = Brush.horizontalGradient(colors = listOf(
                Color(0xFF6FA5FF),
                Color(0xFF4F7DFF)
            )),
            shape = RoundedCornerShape(26.dp))
            .padding(vertical = 16.dp, horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.browse),
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                color = Color.White
            )
        }

        if (AdFusionConfig.openNative()){
            AdFusionController.getNativeAd().NativeAdView(showPadding = true)
        }

        FileTypeGrid(activity = activity)

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = stringResource(R.string.file_management),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )

        // File management options
        FileManagementList(activity = activity)
    }
}

/**
 * Storage usage chart composable
 */
@Composable
fun StorageUsageChart(
    usedPercentage: Float,
    usedSpace: String,
    totalSpace: String,
    isLoading: Boolean
) {
    Box(
        modifier = Modifier
            .padding(8.dp),
        contentAlignment = Alignment.Center
    ) {
        // Shadow background
        Box(
            modifier = Modifier
                .size(185.dp)
                .shadow(
                    elevation = 8.dp,
                    shape = CircleShape,
                    ambientColor = ProgressShadowColor,
                    spotColor = ProgressShadowColor
                )
                .background(
                    color = ProgressBackgroundWhite,
                    shape = CircleShape
                )
        )


        GradientCircularProgressIndicator(
            progress = usedPercentage,
            modifier = Modifier.size(180.dp),
            strokeWidth = 20.dp,
            backgroundStrokeColor = StorageUsageBackgroundColor,
            gradientColors = listOf(ProgressGradientStart, ProgressGradientEnd)
        )

        // Center text
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (!isLoading) {
                Text(
                    text = "${(usedPercentage * 100).toInt()}%",
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.text_black)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "$usedSpace/$totalSpace",
                    fontSize = 14.sp,
                    color = Color(0xFFA3A3A3)
                )
            } else {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
            }
        }
    }
}

/**
 * File type grid composable
 */
@Composable
fun FileTypeGrid(activity: ComponentBaseActivity? = null) {
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            FileTypeItem(
                title = stringResource(R.string.browser_images),
                iconRes = R.mipmap.ic_image,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_IMAGES)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_IMAGES)
                    }
                }
            )
            FileTypeItem(
                title = stringResource(R.string.browser_videos),
                iconRes = R.mipmap.ic_video,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_VIDEOS)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_VIDEOS)
                    }
                }
            )
            FileTypeItem(
                title = stringResource(R.string.browser_audios),
                iconRes = R.mipmap.ic_audio,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_AUDIOS)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_AUDIOS)
                    }
                }
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            FileTypeItem(
                title = stringResource(R.string.browser_docs),
                iconRes = R.mipmap.ic_doc,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_DOCS)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_DOCS)
                    }
                }
            )
            FileTypeItem(
                title = stringResource(R.string.browser_zips),
                iconRes = R.mipmap.ic_zip,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_ZIPS)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_ZIPS)
                    }
                }
            )
            FileTypeItem(
                title = stringResource(R.string.browser_apks),
                iconRes = R.mipmap.ic_apk,
                modifier = Modifier.weight(1f),
                onClick = {
                    if (activity != null) {
                        startFileBrowserActivityWithPermission(activity, FileBrowserActivity.TYPE_APKS)
                    } else {
                        startFileBrowserActivity(context, FileBrowserActivity.TYPE_APKS)
                    }
                }
            )
        }
    }
}

/**
 * File type item composable
 */
@Composable
fun FileTypeItem(
    title: String,
    iconRes: Int,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Image(
                painter = painterResource(id = iconRes),
                contentDescription = title,
                modifier = Modifier.size(46.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = title,
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = colorResource(R.color.text_black)
            )
        }
    }
}

/**
 * File management list composable
 */
@Composable
fun FileManagementList(activity: ComponentBaseActivity? = null) {
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        FileManagementItem(
            title = stringResource(R.string.utility_large_files),
            iconRes = R.mipmap.ic_large,
            onClick = {
                if (activity != null) {
                    startFileUtilityActivityWithPermission(activity, FileUtilityActivity.TYPE_LARGE_FILES)
                } else {
                    startFileUtilityActivity(context, FileUtilityActivity.TYPE_LARGE_FILES)
                }
            }
        )

        FileManagementItem(
            title = stringResource(R.string.utility_recent_files),
            iconRes = R.mipmap.ic_recent,
            onClick = {
                if (activity != null) {
                    startFileUtilityActivityWithPermission(activity, FileUtilityActivity.TYPE_RECENT_FILES)
                } else {
                    startFileUtilityActivity(context, FileUtilityActivity.TYPE_RECENT_FILES)
                }
            }
        )

        FileManagementItem(
            title = stringResource(R.string.utility_duplicate_files),
            iconRes = R.mipmap.ic_duplicate,
            onClick = {
                if (activity != null) {
                    startFileUtilityActivityWithPermission(activity, FileUtilityActivity.TYPE_DUPLICATE_FILES)
                } else {
                    startFileUtilityActivity(context, FileUtilityActivity.TYPE_DUPLICATE_FILES)
                }
            }
        )

        FileManagementItem(
            title = stringResource(R.string.utility_redundant_files),
            iconRes = R.mipmap.ic_redundant,
            onClick = {
                if (activity != null) {
                    startFileUtilityActivityWithPermission(activity, FileUtilityActivity.TYPE_REDUNDANT_FILES)
                } else {
                    startFileUtilityActivity(context, FileUtilityActivity.TYPE_REDUNDANT_FILES)
                }
            }
        )
    }
}

/**
 * File management item composable
 */
@Composable
fun FileManagementItem(
    title: String,
    iconRes: Int,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(13.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(16.dp)) {

            Image(
                painter = painterResource(id = iconRes),
                contentDescription = title,
                modifier = Modifier.size(46.dp)
            )

            Text(
                text = title,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 12.dp),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.text_black)
            )

            Image(
                painter = painterResource(id = R.mipmap.ic_arrow),
                contentDescription = "Arrow",
                modifier = Modifier.size(16.dp)
            )
        }
    }

}

/**
 * Custom gradient circular progress indicator
 */
@Composable
fun GradientCircularProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier,
    strokeWidth: androidx.compose.ui.unit.Dp = 4.dp,
    backgroundStrokeColor: Color = Color.LightGray,
    gradientColors: List<Color> = listOf(Color.Blue, Color.Cyan)
) {
    Canvas(modifier = modifier) {
        val canvasSize = size.minDimension
        val radius = (canvasSize - strokeWidth.toPx()) / 2
        val center = Offset(size.width / 2, size.height / 2)

        // Draw background circle
        drawCircle(
            color = backgroundStrokeColor,
            radius = radius,
            center = center,
            style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
        )

        // Draw gradient progress arc with rotation
        if (progress > 0f) {
            val sweepAngle = 360f * progress.coerceIn(0f, 1f)

            // Rotate the canvas by -90 degrees to start from top
            rotate(-90f, center) {
                // Create sweep gradient
                val gradientBrush = Brush.sweepGradient(
                    colors = gradientColors,
                    center = center
                )

                drawArc(
                    brush = gradientBrush,
                    startAngle = 7f, // Start from 0 since we rotated the canvas
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    topLeft = Offset(
                        center.x - radius,
                        center.y - radius
                    ),
                    size = Size(radius * 2, radius * 2),
                    style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
                )
            }
        }
    }
}
