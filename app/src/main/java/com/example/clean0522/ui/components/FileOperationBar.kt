package com.example.clean0522.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.clean0522.R

/**
 * File operation bar component
 */
@Composable
fun FileOperationBar(
    onDetailClick: () -> Unit,
    onOpenClick: () -> Unit,
    onShareClick: () -> Unit,
    onRenameClick: () -> Unit,
    onRemoveClick: () -> Unit
) {
    Card(elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RectangleShape) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // Details
            OperationButton(
                icon = R.mipmap.con_detail,
                text = stringResource(R.string.file_operation_detail),
                onClick = onDetailClick
            )

            // Open
            OperationButton(
                icon = R.mipmap.con_open,
                text = stringResource(R.string.file_operation_open),
                onClick = onOpenClick
            )

            // Share
            OperationButton(
                icon = R.mipmap.con_share,
                text = stringResource(R.string.file_operation_share),
                onClick = onShareClick
            )

            // Rename
            OperationButton(
                icon = R.mipmap.con_rename,
                text = stringResource(R.string.file_operation_rename),
                onClick = onRenameClick
            )

            // Delete
            OperationButton(
                icon = R.mipmap.con_delete,
                text = stringResource(R.string.file_operation_remove),
                onClick = onRemoveClick
            )
        }
    }
}

/**
 * Operation button component
 */
@Composable
fun OperationButton(
    icon: Int,
    text: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = 4.dp)
            .clickable { onClick() }
    ) {
        Image(painter = painterResource(icon),
            contentDescription = text,
            modifier = Modifier.size(28.dp)
        )

        Text(
            text = text,
            fontSize = 12.sp,
            color = colorResource(R.color.text_black),
            fontWeight = FontWeight.SemiBold
        )
    }
}

/**
 * Batch delete button
 */
@Composable
fun RemoveButton(
    selectedSize: String,
    onClick: () -> Unit
) {
    Card(elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RectangleShape,
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 10.dp, horizontal = 40.dp),
            contentAlignment = Alignment.Center
        ) {
            Button(
                onClick = onClick,
                modifier = Modifier.fillMaxWidth()
                    .height(48.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = colorResource(R.color.text_blue)
                ),
                shape = RoundedCornerShape(13.dp)
            ) {
                Text(
                    text = if (selectedSize.isNotEmpty()) {
                        stringResource(R.string.remove_with_size, selectedSize)
                    } else {
                        stringResource(R.string.remove)
                    },
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}
