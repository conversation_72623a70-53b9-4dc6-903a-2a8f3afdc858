package com.example.clean0522.ui.features.utility

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.domain.model.FileGroup
import com.example.clean0522.domain.model.FileItem
import com.example.clean0522.domain.model.LargeFileType
import com.example.clean0522.domain.model.SortOption
import com.example.clean0522.utils.FileAnalyzer
import com.example.clean0522.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * ViewModel for file utility features
 */
class FileUtilityViewModel : ViewModel() {

    // Large Files
    var largeFiles by mutableStateOf<List<FileItem>>(emptyList())
        private set

    var selectedLargeFileType by mutableStateOf(LargeFileType.ALL)
        private set

    var filteredLargeFiles by mutableStateOf<List<FileItem>>(emptyList())
        private set

    // Recent Files
    var recentFiles by mutableStateOf<List<FileItem>>(emptyList())
        private set

    // Duplicate Files
    var duplicateFileGroups by mutableStateOf<List<FileGroup>>(emptyList())
        private set

    // Redundant Files
    var redundantFileGroups by mutableStateOf<List<FileGroup>>(emptyList())
        private set

    // Similar Photos
    var similarPhotoGroups by mutableStateOf<List<FileGroup>>(emptyList())
        private set

    // Common states
    var isLoading by mutableStateOf(false)
        private set

    var isSelectionMode by mutableStateOf(false)
        private set

    var selectedFiles by mutableStateOf<Set<FileItem>>(emptySet())
        private set

    var selectedGroups by mutableStateOf<Set<String>>(emptySet())
        private set

    var expandedGroups by mutableStateOf<Set<String>>(emptySet())
        private set

    var currentSelectedFile by mutableStateOf<FileItem?>(null)
        private set

    // Dialog states
    var showDetailsDialog by mutableStateOf(false)
        private set

    var showRenameDialog by mutableStateOf(false)
        private set

    var showDeleteDialog by mutableStateOf(false)
        private set

    // Sort states
    var currentSortOption by mutableStateOf(SortOption.NAME_A_TO_Z)
        private set

    var showSortDialog by mutableStateOf(false)
        private set

    /**
     * Load large files
     */
    fun loadLargeFiles() {
        viewModelScope.launch {
            isLoading = true
            try {
                val files = withContext(Dispatchers.IO) {
                    FileAnalyzer.findLargeFiles()
                }
                largeFiles = files
                filterLargeFiles()
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Filter large files by type
     */
    fun selectLargeFileType(type: LargeFileType) {
        selectedLargeFileType = type
        filterLargeFiles()
    }

    private fun filterLargeFiles() {
        val filtered = FileAnalyzer.filterLargeFilesByType(largeFiles, selectedLargeFileType)
        filteredLargeFiles = sortFiles(filtered)
    }

    /**
     * Load recent files
     */
    fun loadRecentFiles() {
        viewModelScope.launch {
            isLoading = true
            try {
                val files = withContext(Dispatchers.IO) {
                    FileAnalyzer.findRecentFiles()
                }
                recentFiles = sortFiles(files)
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load duplicate files
     */
    fun loadDuplicateFiles() {
        viewModelScope.launch {
            isLoading = true
            try {
                val groups = withContext(Dispatchers.IO) {
                    FileAnalyzer.findDuplicateFiles()
                }
                duplicateFileGroups = groups
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load redundant files
     */
    fun loadRedundantFiles() {
        viewModelScope.launch {
            isLoading = true
            try {
                val groups = withContext(Dispatchers.IO) {
                    FileAnalyzer.findRedundantFiles()
                }
                redundantFileGroups = groups
                // Redundant files always in selection mode
                isSelectionMode = true
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Load similar photos
     */
    fun loadSimilarPhotos(context: Context) {
        viewModelScope.launch {
            isLoading = true
            try {
                val groups = withContext(Dispatchers.IO) {
                    FileAnalyzer.findSimilarPhotos(context)
                }
                similarPhotoGroups = groups
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * Toggle group expansion
     */
    fun toggleGroupExpansion(groupId: String) {
        expandedGroups = if (expandedGroups.contains(groupId)) {
            expandedGroups - groupId
        } else {
            expandedGroups + groupId
        }
    }

    /**
     * Toggle group selection
     */
    fun toggleGroupSelection(group: FileGroup) {
        if (selectedGroups.contains(group.id)) {
            selectedGroups = selectedGroups - group.id
            selectedFiles = selectedFiles - group.files.toSet()
        } else {
            selectedGroups = selectedGroups + group.id
            selectedFiles = selectedFiles + group.files.toSet()
        }
    }

    /**
     * Handle file click
     */
    fun onFileClick(context: Context, fileItem: FileItem) {
        currentSelectedFile = fileItem
        if (isSelectionMode) {
            toggleFileSelection(fileItem)
        } else {
            FileUtils.openFile(context, fileItem.file)
        }
    }

    /**
     * Handle file long click
     */
    fun onFileLongClick(fileItem: FileItem) {
        isSelectionMode = true
        currentSelectedFile = fileItem
        selectedFiles = selectedFiles + fileItem
    }

    fun changeSelectMode(isSelect: Boolean){
        isSelectionMode = isSelect
    }

    /**
     * Toggle file selection
     */
    fun toggleFileSelection(fileItem: FileItem) {
        selectedFiles = if (selectedFiles.contains(fileItem)) {
            selectedFiles - fileItem
        } else {
            selectedFiles + fileItem
        }

        currentSelectedFile = if (selectedFiles.isEmpty()) {
            null
        }else{
            selectedFiles.elementAt(0)
        }
    }

    /**
     * Exit selection mode
     */
    fun exitSelectionMode() {
        isSelectionMode = false
        selectedFiles = emptySet()
        selectedGroups = emptySet()
        currentSelectedFile = null
    }

    /**
     * Toggle select all
     */
    fun toggleSelectAll(allFiles: List<FileItem>) {
        selectedFiles = if (selectedFiles.size == allFiles.size) {
            emptySet()
        } else {
            allFiles.toSet()
        }

        if (selectedFiles.isEmpty()) {
            exitSelectionMode()
        }
    }

    /**
     * Get selected files size
     */
    fun getSelectedFilesSize(): String {
        val totalSize = selectedFiles.sumOf { it.size }
        return FileUtils.formatFileSize(totalSize)
    }

    /**
     * Show file details dialog
     */
    fun showFileDetails() {
        showDetailsDialog = true
    }

    /**
     * Dismiss file details dialog
     */
    fun dismissFileDetails() {
        showDetailsDialog = false
    }

    /**
     * Show rename file dialog
     */
    fun showRenameFile() {
        showRenameDialog = true
    }

    /**
     * Dismiss rename file dialog
     */
    fun dismissRenameFile() {
        showRenameDialog = false
    }

    /**
     * Rename file
     */
    fun renameFile(newName: String) {
        currentSelectedFile?.let { fileItem ->
            viewModelScope.launch(Dispatchers.IO) {
                val file = File(fileItem.path)
                val success = FileUtils.renameFile(file, newName)

                withContext(Dispatchers.Main) {
                    if (success) {
                        // Refresh the current list
                        refreshCurrentList()
                    }
                    dismissRenameFile()
                    exitSelectionMode()
                }
            }
        } ?: dismissRenameFile()
    }

    /**
     * Show delete confirmation dialog
     */
    fun showDeleteConfirmation() {
        showDeleteDialog = true
    }

    /**
     * Dismiss delete confirmation dialog
     */
    fun dismissDeleteConfirmation() {
        showDeleteDialog = false
    }

    /**
     * Delete selected files
     */
    fun deleteSelectedFiles() {
        if (selectedFiles.isNotEmpty()) {
            viewModelScope.launch(Dispatchers.IO) {
                val filesToDelete = selectedFiles.toList()

                filesToDelete.forEach { fileItem ->
                    val file = File(fileItem.path)
                    FileUtils.deleteFile(file)
                }

                withContext(Dispatchers.Main) {
                    refreshCurrentList()
                    exitSelectionMode()
                }
            }
        }
        dismissDeleteConfirmation()
    }

    /**
     * Refresh current list based on what's currently loaded
     */
    private fun refreshCurrentList(context: Context? = null) {
        when {
            largeFiles.isNotEmpty() -> loadLargeFiles()
            recentFiles.isNotEmpty() -> loadRecentFiles()
            duplicateFileGroups.isNotEmpty() -> loadDuplicateFiles()
            redundantFileGroups.isNotEmpty() -> loadRedundantFiles()
            similarPhotoGroups.isNotEmpty() -> context?.let { loadSimilarPhotos(it) }
        }
    }

    /**
     * Show sort dialog
     */
    fun showSortDialog() {
        showSortDialog = true
    }

    /**
     * Dismiss sort dialog
     */
    fun dismissSortDialog() {
        showSortDialog = false
    }

    /**
     * Set sort option and re-sort files
     */
    fun setSortOption(sortOption: SortOption) {
        currentSortOption = sortOption
        applySorting()
    }

    /**
     * Apply current sorting to file lists
     */
    private fun applySorting() {
        // Re-sort large files
        if (largeFiles.isNotEmpty()) {
            filterLargeFiles()
        }

        // Re-sort recent files
        if (recentFiles.isNotEmpty()) {
            recentFiles = sortFiles(recentFiles)
        }
    }

    /**
     * Sort files based on current sort option
     */
    private fun sortFiles(files: List<FileItem>): List<FileItem> {
        return when (currentSortOption) {
            SortOption.NAME_A_TO_Z -> files.sortedBy { it.name.lowercase() }
            SortOption.NAME_Z_TO_A -> files.sortedByDescending { it.name.lowercase() }
            SortOption.NEWEST_FIRST -> files.sortedByDescending { it.lastModified }
            SortOption.OLDEST_FIRST -> files.sortedBy { it.lastModified }
            SortOption.SMALL_TO_LARGE -> files.sortedBy { it.size }
            SortOption.LARGE_TO_SMALL -> files.sortedByDescending { it.size }
        }
    }
}
