package com.example.clean0522.ui.features.browser

import android.content.Context
import android.os.Environment
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.clean0522.domain.model.FileItem
import com.example.clean0522.domain.model.SortOption
import com.example.clean0522.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * ViewModel for the StorageBrowser
 */
class StorageBrowserViewModel : ViewModel() {

    // Current directory path
    var currentPath by mutableStateOf(Environment.getExternalStorageDirectory().absolutePath)
        private set

    // List of files in the current directory
    var fileList by mutableStateOf<List<FileItem>>(emptyList())
        private set

    // Whether selection mode is active
    var isSelectionMode by mutableStateOf(false)
        private set

    // Set of selected files
    var selectedFiles by mutableStateOf<Set<FileItem>>(emptySet())
        private set

    // Currently selected single file (for operation bar)
    var currentSelectedFile by mutableStateOf<FileItem?>(null)
        private set

    // Dialog states
    var showDetailsDialog by mutableStateOf(false)
        private set

    var showRenameDialog by mutableStateOf(false)
        private set

    var showDeleteDialog by mutableStateOf(false)
        private set

    // Sort state
    var currentSortOption by mutableStateOf(SortOption.NAME_A_TO_Z)
        private set

    var showSortDialog by mutableStateOf(false)
        private set

    init {
        loadFiles()
    }

    /**
     * Load files from the current directory
     */
    fun loadFiles() {
        viewModelScope.launch(Dispatchers.IO) {
            val directory = File(currentPath)

            // Use async method to get file list
            val files = FileUtils.getFilesInDirectoryAsync(directory)

            // Apply sorting
            val sortedFiles = sortFiles(files)

            // Switch back to main thread to update UI
            kotlinx.coroutines.withContext(Dispatchers.Main) {
                fileList = sortedFiles
            }
        }
    }

    /**
     * Navigate to the specified directory
     */
    fun navigateToDirectory(path: String) {
        // Update current path
        currentPath = path

        // Load files from the new directory
        loadFiles()

        // Exit selection mode
        exitSelectionMode()
    }

    /**
     * Check if current directory is a root directory
     */
    fun isRootDirectory(): Boolean {
        val externalStoragePath = Environment.getExternalStorageDirectory().absolutePath
        return currentPath == externalStoragePath || currentPath == "/storage/emulated/0" || currentPath == "/storage"
    }

    /**
     * Navigate to the parent directory
     */
    fun navigateToParentDirectory() {
        val parentFile = File(currentPath).parentFile
        if (parentFile != null && parentFile.exists()) {
            navigateToDirectory(parentFile.absolutePath)
        }
    }

    /**
     * Open a file or directory
     */
    fun openFileOrDirectory(context: Context, fileItem: FileItem) {
        if (isSelectionMode) {
            toggleFileSelection(fileItem)
            return
        }

        if (fileItem.isDirectory) {
            navigateToDirectory(fileItem.path)
        } else {
            currentSelectedFile = fileItem
            FileUtils.openFile(context, fileItem.file)
        }
    }

    /**
     * Handle long click on a file or directory
     */
    fun onFileLongClick(fileItem: FileItem) {
        // Enter selection mode
        isSelectionMode = true

        // Set current selected file
        currentSelectedFile = fileItem

        // Select the file
        selectedFiles = selectedFiles + fileItem
    }

    /**
     * Toggle file selection state
     */
    fun toggleFileSelection(fileItem: FileItem) {
        currentSelectedFile = fileItem
        selectedFiles = if (selectedFiles.contains(fileItem)) {
            selectedFiles - fileItem
        } else {
            selectedFiles + fileItem
        }

        // If no files are selected, exit selection mode
        currentSelectedFile = if (selectedFiles.isEmpty()) {
            null
        }else{
            selectedFiles.elementAt(0)
        }
    }

    /**
     * Exit selection mode
     */
    fun exitSelectionMode() {
        isSelectionMode = false
        selectedFiles = emptySet()
        currentSelectedFile = null
    }

    /**
     * Toggle select all/deselect all
     */
    fun toggleSelectAll() {
        selectedFiles = if (selectedFiles.size == fileList.size) {
            emptySet()
        } else {
            fileList.toSet()
        }

        // If no files are selected, exit selection mode
        if (selectedFiles.isEmpty()) {
            currentSelectedFile = null
        }
    }

    /**
     * Get total size of selected files
     */
    fun getSelectedFilesSize(): String {
        val totalSize = selectedFiles.sumOf { it.size }
        return FileUtils.formatFileSize(totalSize)
    }

    /**
     * Show file details dialog
     */
    fun showFileDetails() {
        showDetailsDialog = true
    }

    /**
     * Dismiss file details dialog
     */
    fun dismissFileDetails() {
        showDetailsDialog = false
    }

    /**
     * Show rename file dialog
     */
    fun showRenameFile() {
        showRenameDialog = true
    }

    /**
     * Dismiss rename file dialog
     */
    fun dismissRenameFile() {
        showRenameDialog = false
    }

    /**
     * Rename a file
     */
    fun renameFile(newName: String) {
        currentSelectedFile?.let { fileItem ->
            // Use coroutine to rename file in background thread
            viewModelScope.launch(Dispatchers.IO) {
                val file = File(fileItem.path)
                val success = FileUtils.renameFile(file, newName)

                // Switch back to main thread to update UI
                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    if (success) {
                        // Reload file list after successful rename
                        loadFiles()
                    }
                    dismissRenameFile()
                    exitSelectionMode()
                }
            }
        } ?: dismissRenameFile()
    }

    /**
     * Show delete confirmation dialog
     */
    fun showDeleteConfirmation() {
        showDeleteDialog = true
    }

    /**
     * Dismiss delete confirmation dialog
     */
    fun dismissDeleteConfirmation() {
        showDeleteDialog = false
    }

    /**
     * Delete selected files
     */
    fun deleteSelectedFiles() {
        if (selectedFiles.isNotEmpty()) {
            // Use coroutine to delete files in background thread
            viewModelScope.launch(Dispatchers.IO) {
                // Make a copy of selected files to avoid concurrent modification
                val filesToDelete = selectedFiles.toList()

                // Delete files
                filesToDelete.forEach { fileItem ->
                    val file = File(fileItem.path)
                    FileUtils.deleteFile(file)
                }

                // Switch back to main thread to update UI
                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    // Reload file list after deletion
                    loadFiles()
                    // Exit selection mode
                    exitSelectionMode()
                }
            }
        }
        dismissDeleteConfirmation()
    }

    /**
     * Show sort dialog
     */
    fun showSortDialog() {
        showSortDialog = true
    }

    /**
     * Dismiss sort dialog
     */
    fun dismissSortDialog() {
        showSortDialog = false
    }

    /**
     * Set sort option and re-sort files
     */
    fun setSortOption(sortOption: SortOption) {
        currentSortOption = sortOption
        applySorting()
    }

    /**
     * Apply current sorting to file list
     */
    private fun applySorting() {
        fileList = sortFiles(fileList)
    }

    /**
     * Sort files based on current sort option
     */
    private fun sortFiles(files: List<FileItem>): List<FileItem> {
        return when (currentSortOption) {
            SortOption.NAME_A_TO_Z -> files.sortedWith(compareBy({ !it.isDirectory }, { it.name.lowercase() }))
            SortOption.NAME_Z_TO_A -> {
                val directories = files.filter { it.isDirectory }.sortedByDescending { it.name.lowercase() }
                val regularFiles = files.filter { !it.isDirectory }.sortedByDescending { it.name.lowercase() }
                directories + regularFiles
            }
            SortOption.NEWEST_FIRST -> files.sortedWith(compareBy({ !it.isDirectory }, { -it.lastModified }))
            SortOption.OLDEST_FIRST -> files.sortedWith(compareBy({ !it.isDirectory }, { it.lastModified }))
            SortOption.SMALL_TO_LARGE -> files.sortedWith(compareBy({ !it.isDirectory }, { it.size }))
            SortOption.LARGE_TO_SMALL -> files.sortedWith(compareBy({ !it.isDirectory }, { -it.size }))
        }
    }
}
