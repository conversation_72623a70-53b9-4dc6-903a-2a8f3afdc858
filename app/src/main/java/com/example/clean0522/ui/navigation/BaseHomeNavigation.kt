package com.example.clean0522.ui.navigation

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.clean0522.ComponentBaseActivity
import com.example.clean0522.FileBrowserActivity
import com.example.clean0522.FileUtilityActivity
import com.example.clean0522.MainActivity
import com.example.clean0522.ResultActivity
import com.example.clean0522.SystemInfoActivity
import com.example.clean0522.ui.features.files.FilesViewModel
import com.example.clean0522.ui.features.antivirus.AntivirusScreen
import com.example.clean0522.ui.features.more.MoreScreen
import com.example.clean0522.ui.features.files.FilesScreen
import com.example.clean0522.ui.features.settings.SettingsScreen

/**
 * Main navigation component
 */
@Composable
fun BaseHomeNavigation(
    navController: NavHostController,
    filesViewModel: FilesViewModel = viewModel(),
    activity: ComponentBaseActivity? = null,
    onRequestAntivirusPrivacyDialog: (String) -> Unit = {}
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Files.route
    ) {
        composable(Screen.Files.route) {
            FilesScreen(
                viewModel = filesViewModel,
                activity = activity
            )
        }

        composable(Screen.Antivirus.route) {
            AntivirusScreen(
                activity = activity,
                onRequestPrivacyDialog = onRequestAntivirusPrivacyDialog
            )
        }

        composable(Screen.More.route) {
            MoreScreen(activity = activity)
        }

        composable(Screen.Settings.route) {
            SettingsScreen(
                activity = activity
            )
        }
    }
}

fun startFileBrowserActivity(context: Context, browserType: String) {
    val intent = Intent(context, FileBrowserActivity::class.java)
    intent.putExtra(FileBrowserActivity.EXTRA_BROWSER_TYPE, browserType)
    context.startActivity(intent)
    try {
        if (context as Activity !is MainActivity && context !is SystemInfoActivity && context !is ResultActivity){
            context.finish()
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun startFileUtilityActivity(context: Context, utilityType: String) {
    val intent = Intent(context, FileUtilityActivity::class.java)
    intent.putExtra(FileUtilityActivity.EXTRA_UTILITY_TYPE, utilityType)
    context.startActivity(intent)
    try {
        if (context as Activity !is MainActivity && context !is SystemInfoActivity && context !is ResultActivity){
            context.finish()
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * Start file browser activity with permission check
 * @param activity ComponentBaseActivity instance for permission handling
 * @param browserType Type of browser to start
 */
fun startFileBrowserActivityWithPermission(activity: ComponentBaseActivity, browserType: String) {
    activity.checkStoragePermissionAndExecute {
        startFileBrowserActivity(activity, browserType)
    }
}

/**
 * Start file utility activity with permission check
 * @param activity ComponentBaseActivity instance for permission handling
 * @param utilityType Type of utility to start
 */
fun startFileUtilityActivityWithPermission(activity: ComponentBaseActivity, utilityType: String) {
    activity.checkStoragePermissionAndExecute {
        startFileUtilityActivity(activity, utilityType)
    }
}
