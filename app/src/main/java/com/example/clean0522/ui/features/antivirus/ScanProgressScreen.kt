package com.example.clean0522.ui.features.antivirus

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.example.clean0522.R
import com.example.clean0522.domain.model.ScanResult
import com.example.clean0522.domain.model.ScanState

/**
 * Screen showing scan progress with Lottie animation
 */
@Composable
fun ScanProgressScreen(
    scanResult: ScanResult,
    scanType: String,
    onCancelClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Lottie animation composition
    val composition by rememberLottieComposition(
        LottieCompositionSpec.RawRes(R.raw.scanning_animation) // You'll need to add this animation file
    )
    
    val progress by animateLottieCompositionAsState(
        composition = composition,
        isPlaying = scanResult.scanState == ScanState.SCANNING,
        iterations = LottieConstants.IterateForever
    )
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Lottie Animation
        LottieAnimation(
            composition = composition,
            progress = { progress },
            modifier = Modifier
                .size(200.dp)
                .padding(bottom = 32.dp)
        )
        
        // Scan Title
        Text(
            text = when (scanType) {
                "quick" -> stringResource(R.string.scan_type_quick)
                "folder" -> stringResource(R.string.scan_type_folder)
                "complete" -> stringResource(R.string.scan_type_complete)
                else -> stringResource(R.string.scan_type_quick)
            },
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // Scan Status
        Text(
            text = when (scanResult.scanState) {
                ScanState.SCANNING -> stringResource(R.string.scan_in_progress)
                ScanState.COMPLETED -> stringResource(R.string.scan_completed)
                ScanState.ERROR -> stringResource(R.string.scan_error)
                ScanState.CANCELLED -> stringResource(R.string.scan_cancelled)
                else -> stringResource(R.string.initializing_scan)
            },
            fontSize = 18.sp,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Progress Information
        if (scanResult.scanState == ScanState.SCANNING) {
            Text(
                text = stringResource(R.string.scanning_files),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // Progress Percentage
            if (scanResult.progress > 0) {
                Text(
                    text = stringResource(R.string.scan_progress, scanResult.progress),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // Progress Bar
                LinearProgressIndicator(
                    progress = { scanResult.progress / 100f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp, vertical = 8.dp)
                )
            } else {
                // Indeterminate progress
                LinearProgressIndicator(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp, vertical = 8.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Cancel Button (only show during scanning)
        if (scanResult.scanState == ScanState.SCANNING) {
            OutlinedButton(
                onClick = onCancelClick,
                modifier = Modifier.padding(top = 16.dp)
            ) {
                Text(
                    text = stringResource(R.string.cancel_scan),
                    fontSize = 16.sp
                )
            }
        }
        
        // Error Message
        if (scanResult.scanState == ScanState.ERROR && scanResult.errorMessage != null) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onCancelClick() }
                    .padding(top = 16.dp)
                    .padding(horizontal = 30.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFFFF505E)
                ),
            ) {
                Text(
                    text = stringResource(R.string.ok),
                    fontSize = 16.sp,
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(16.dp)
                        .align(Alignment.CenterHorizontally)
                )
            }
        }
    }
}
