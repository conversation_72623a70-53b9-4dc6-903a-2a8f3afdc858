package com.ext.adfusion.util

import com.tencent.mmkv.MMKV

/**
 * Manager for terms and privacy agreement preferences using MMKV
 */
class AdFusionPreferences private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val AD_SHOW_LAST_TIME = "ad_show_last_time"
        private const val AD_VALUE_TOTAL = "ad_value_total"
        private const val AD_AF_TOTAL_REVENUE = "ad_af_total_revenue"
        private const val AD_UMP_QUE_TIME = "ad_ump_que_time"
        
        @Volatile
        private var INSTANCE: AdFusionPreferences? = null

        val instance: AdFusionPreferences by lazy {
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: AdFusionPreferences().also { INSTANCE = it }
            }
        }
    }

    fun setAdShowLastTime(time: Long) {
        mmkv.putLong(AD_SHOW_LAST_TIME, time)
    }

    fun getAdShowLastTime(): Long {
        return mmkv.getLong(AD_SHOW_LAST_TIME, 0)
    }

    fun setAdValueTotal(value: Float) {
        mmkv.putFloat(AD_VALUE_TOTAL, value)
    }

    fun getAdValueTotal(): Float {
        return mmkv.getFloat(AD_VALUE_TOTAL, 0f)
    }

    fun setAdUMPQueTime(time: Long) {
        mmkv.putLong(AD_UMP_QUE_TIME, time)
    }

    fun getAdUMPQueTime(): Long {
        return mmkv.getLong(AD_UMP_QUE_TIME, 0)
    }

    fun setAdAFTotalRevenue(value: Float) {
        mmkv.putFloat(AD_AF_TOTAL_REVENUE, value)
    }

    fun getAdAFTotalRevenue(): Float {
        return mmkv.getFloat(AD_AF_TOTAL_REVENUE, 0f)
    }
}
