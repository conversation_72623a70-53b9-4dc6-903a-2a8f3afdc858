package com.ext.adfusion.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

object AdEventManager {

    private val _adEventFlow = MutableSharedFlow<AdEventData>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val adEventFlow: SharedFlow<AdEventData> = _adEventFlow.asSharedFlow()


    suspend fun adInit() {
        _adEventFlow.emit(AdEventData.Init)
    }

    fun triggerAdInit() {
        CoroutineScope(Dispatchers.Default).launch {
            try {
                adInit()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}

sealed class AdEventData {
    object Init : AdEventData()
}
