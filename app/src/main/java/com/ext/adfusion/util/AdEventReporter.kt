package com.ext.adfusion.util

import com.applovin.mediation.MaxAd
import com.ext.FlyerUtil
import com.ext.firbase.UpdateEvent
import com.ext.remoteset.AnalyticsManager
import com.google.android.gms.ads.AdValue

object AdEventReporter {
    fun postPaidEventByAdmob(adValue: AdValue, network: String, adType: String) {
        AnalyticsManager.sendAdmobRevenue(adValue, network, adType)
        UpdateEvent.admobAdImpression(adValue, network, adType)
        UpdateEvent.updateAdmobAdRevenue(adValue, network, adType)
        UpdateEvent.updateTotalRevenue(adValue.valueMicros.toDouble() / 1000000)
        FlyerUtil.updateAFTotalRevenue(adValue.valueMicros.toDouble() / 1000000)
        FlyerUtil.admobRoseValue(adValue, network, adType)
    }

    fun postPaidEventByMax(maxAd: MaxAd) {
        AnalyticsManager.sendMaxAdRevenue(maxAd)
        UpdateEvent.maxAdImpression(maxAd)
        UpdateEvent.updateMaxAdRevenue(maxAd)
        UpdateEvent.updateTotalRevenue(maxAd.revenue)
        FlyerUtil.updateAFTotalRevenue(maxAd.revenue)
        FlyerUtil.maxRoseValue(maxAd)
    }

    fun analysisPostEvent(event: String) {
        UpdateEvent.event(event)
    }
}