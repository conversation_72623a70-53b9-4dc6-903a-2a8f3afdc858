package com.ext.adfusion.util

import com.applovin.mediation.MaxAd
import com.ext.FlyerUtil
import com.ext.firbase.UpdateEvent
import com.ext.remoteset.AnalyticsManager
import com.google.android.gms.ads.AdValue
import com.tradplus.ads.base.bean.TPAdInfo

object AdEventReporter {
    fun postPaidEventByAdmob(adValue: AdValue, network: String, adType: String) {
        AnalyticsManager.sendAdmobRevenue(adValue, network, adType)
        UpdateEvent.admobAdImpression(adValue, network, adType)
        UpdateEvent.updateAdmobAdRevenue(adValue, network, adType)
        UpdateEvent.updateTotalRevenue(adValue.valueMicros.toDouble() / 1000000)
        FlyerUtil.updateAFTotalRevenue(adValue.valueMicros.toDouble() / 1000000)
        FlyerUtil.admobRoseValue(adValue, network, adType)
    }

    fun postPaidEventByMax(maxAd: MaxAd) {
        AnalyticsManager.sendMaxAdRevenue(maxAd)
        UpdateEvent.maxAdImpression(maxAd)
        UpdateEvent.updateMaxAdRevenue(maxAd)
        UpdateEvent.updateTotalRevenue(maxAd.revenue)
        FlyerUtil.updateAFTotalRevenue(maxAd.revenue)
        FlyerUtil.maxRoseValue(maxAd)
    }

    fun postPaidEventByTradPlus(tpAdInfo: TPAdInfo?, adType: String) {
        // TRADPlus revenue reporting
        // Note: TRADPlus may not provide direct revenue info like Max/AdMob
        // You may need to implement custom revenue tracking based on your TRADPlus setup
        tpAdInfo?.let { adInfo ->
            // Log the impression for analytics
            analysisPostEvent("tradplus_${adType}_impression")

            // If TRADPlus provides revenue info in the future, add it here
            // For now, we'll just track the impression
        }
    }

    fun analysisPostEvent(event: String) {
        UpdateEvent.event(event)
    }
}