package com.ext.adfusion.util

import android.util.Log
import com.example.clean0522.BuildConfig

object AdFusionLogger {
    var showLogger = BuildConfig.DEBUG

    fun Any.logAd(message: String) {
        if (showLogger) {
            Log.d(this::class.java.simpleName, message)
        }
    }

    fun Any.logAd(tag: String, message: String) {
        if (showLogger) {
            Log.d(tag, message)
        }
    }
}