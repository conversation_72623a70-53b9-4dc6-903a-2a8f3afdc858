package com.ext.adfusion.tradplus

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.RewardedAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.reward.RewardAdListener
import com.tradplus.ads.open.reward.TPReward

private const val TAG = "TradPlusRewardedAd"

/**
 * TRADPlus implementation of RewardedAdAdapter
 */
class TradPlusRewardedAdapter private constructor() : RewardedAdAdapter {

    private var tpReward: TPReward? = null
    private var currentAdUnitId: String? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 50 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME && currentAdUnitId == adUnitId) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 50 minutes), loading new ad")
                tpReward = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        // Create new rewarded ad
        // Note: TPReward constructor takes (Activity, adUnitId) but we pass null for Activity
        // This may cause issues with some ad networks that require Activity context
        tpReward = TPReward(null, adUnitId)

        tpReward?.setAdListener(object : RewardAdListener {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad loaded")
                isLoading = false
                isAdShown = false
                adLoadedTimestamp = System.currentTimeMillis()
                adListener?.onAdLoaded()
            }

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad displayed")
                isAdShown = true
                adListener?.onAdDisplay()
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "rewarded")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad hidden")
                adListener?.onAdClosed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdFailed(error: TPAdError?) {
                logAd(TAG, "Rewarded ad failed to load: ${error?.errorMsg}")
                isLoading = false
                adListener?.onAdFailedToLoad()
            }

            override fun onAdReward(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "User rewarded")
                adListener?.onRewarded()
            }

            override fun onAdVideoStart(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad video started")
            }

            override fun onAdVideoEnd(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad video ended")
            }

            override fun onAdVideoError(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                logAd(TAG, "Rewarded ad video error: ${error?.errorMsg}")
            }
        })

        tpReward?.loadAd()
    }

    override fun showAd(
        activity: Activity,
        onRewarded: (amount: Int) -> Unit,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (tpReward == null || tpReward?.isReady != true) {
            logAd(TAG, "The rewarded ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        tpReward?.setAdListener(object : RewardAdListener {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?) {
            }

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad displayed")
                isAdShown = true
                adListener?.onAdDisplay()
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "rewarded")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad hidden")
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdFailed(error: TPAdError?) {
            }

            override fun onAdReward(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "User rewarded")
                adListener?.onRewarded()
                onRewarded(1) // TRADPlus doesn't provide amount, default to 1
            }

            override fun onAdVideoStart(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad video started")
            }

            override fun onAdVideoEnd(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Rewarded ad video ended")
            }

            override fun onAdVideoError(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                logAd(TAG, "Rewarded ad failed to display: ${error?.errorMsg}")
            }
        })

        tpReward?.showAd(activity, null)
    }

    override fun isAdLoaded(): Boolean {
        return tpReward?.isReady == true
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            tpReward = null
            instance = null
            isAdShown = false
            return
        }

        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                tpReward = null
                isAdShown = false
                instance = null
                return
            }
        }

        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            tpReward = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: TradPlusRewardedAdapter? = null

        fun getInstance(): TradPlusRewardedAdapter {
            return instance ?: synchronized(this) {
                TradPlusRewardedAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getRewardedId())
        }
    }
}
