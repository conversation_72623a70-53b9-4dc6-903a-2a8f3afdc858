package com.ext.adfusion.tradplus

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.view.ViewGroup
import android.widget.FrameLayout
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AppOpenAdAdapter
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.adfusion.util.AdEventReporter
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.splash.SplashAdListener
import com.tradplus.ads.open.splash.TPSplash

/**
 * TRADPlus implementation of AppOpenAdAdapter
 */
class TradPlusAppOpenAdapter private constructor() : AppOpenAdAdapter {

    private var tpSplash: TPSplash? = null
    private var currentAdUnitId: String? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 50 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false
    private var adContainer: ViewGroup? = null
    private var pendingDismissCallback: (() -> Unit)? = null
    private var pendingFailCallback: (() -> Unit)? = null

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 50 minutes), loading new ad")
                tpSplash = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        // Create a new splash ad
        // Note: TPSplash constructor takes (Activity, adUnitId) but we pass null for Activity
        // This may cause issues with some ad networks that require Activity context
        tpSplash = TPSplash(null, adUnitId)

        tpSplash?.setAdListener(object : SplashAdListener() {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {
                logAd(TAG, "App open ad loaded")
                isLoading = false
                adLoadedTimestamp = System.currentTimeMillis()
                isAdShown = false
                adListener?.onAdLoaded()
            }

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "App open ad displayed")
                isAdShown = true
                adListener?.onAdDisplay()
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "app_open")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "App open ad hidden")
                // Load the next ad
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "App open ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(error: TPAdError?) {
                logAd(TAG, "App open ad failed to load: ${error?.errorMsg}")
                isLoading = false
                adListener?.onAdFailedToLoad()
            }

            override fun onAdShowFailed(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                logAd(TAG, "App open ad failed to display: ${error?.errorMsg}")
                // Retry loading the ad
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }
        })

        tpSplash?.loadAd(null)
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (!isAdLoaded()) {
            logAd(TAG, "The app open ad is not ready yet.")
            onAdFailedToShow()

            // Try to load a new ad
            currentAdUnitId?.let { loadAd(it) }
            return
        }

        // Create a container for the splash ad
        adContainer = FrameLayout(activity).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }

        // Set a listener for the ad dismiss event
        tpSplash?.setAdListener(object : SplashAdListener() {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {}

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                adListener?.onAdDisplay()
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "app_open")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(error: TPAdError?) {}

            override fun onAdShowFailed(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                onAdFailedToShow()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }
        })

        // Add container to activity and show ad
        val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
        rootView.addView(adContainer)
        tpSplash?.showAd(adContainer)
    }

    override fun isAdLoaded(): Boolean {
        if (tpSplash?.isReady() != true) return false

        val currentTime = System.currentTimeMillis()
        val adAge = currentTime - adLoadedTimestamp
        return adAge < AD_EXPIRY_TIME
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            tpSplash = null
            instance = null
            isAdShown = false
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                tpSplash = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            tpSplash = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        private const val TAG = "TradPlusAppOpenAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: TradPlusAppOpenAdapter? = null

        fun getInstance(): TradPlusAppOpenAdapter {
            return instance ?: synchronized(this) {
                TradPlusAppOpenAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getAppOpenId())
        }
    }
}
