package com.ext.adfusion.tradplus

import android.util.Log
import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.example.clean0522.R
import com.ext.adfusion.interfaces.BannerAdAdapter
import com.ext.adfusion.util.AdEventReporter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.banner.BannerAdListener
import com.tradplus.ads.open.banner.TPBanner


private const val TAG = "TradPlusBannerAd"

class TradPlusBannerAdapter() : BannerAdAdapter {

    override fun loadAd(adUnitId: String) {
        // Banner ads are loaded automatically when BannerAdView is created
    }

    @Composable
    override fun BannerAdView(adUnitId: String, modifier: Modifier) {
        var isAdLoaded by remember { mutableStateOf(false) }
        var tpBanner by remember { mutableStateOf<TPBanner?>(null) }
        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        logAd(TAG, "Banner ad resumed")
                    }

                    Lifecycle.Event.ON_PAUSE -> {
                        logAd(TAG, "Banner ad paused")
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        tpBanner?.onDestroy()
                        logAd(TAG, "Banner ad destroyed")
                    }

                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
                tpBanner?.onDestroy()
            }
        }
        Log.d(TAG, "BannerAdView: 1111111111111111111111")

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(70.dp)
                .background(color = colorResource(R.color.show_ad_bg))
                .padding(5.dp)
                .then(modifier),
            contentAlignment = Alignment.Center
        ) {
            if (!isAdLoaded) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("ad/banner_small.json")
                )

                LottieAnimation(
                    composition = composition,
                    iterations = LottieConstants.IterateForever
                )
            }

            AndroidView(
                factory = { context ->
                    // Create TPBanner
                    val banner = TPBanner(context).apply {
                        tpBanner = this

                        layoutParams = FrameLayout.LayoutParams(
                            FrameLayout.LayoutParams.WRAP_CONTENT,
                            FrameLayout.LayoutParams.WRAP_CONTENT
                        ).apply {
                            gravity = Gravity.CENTER_HORIZONTAL
                        }

                        setAdListener(object : BannerAdListener() {
                            override fun onAdLoaded(tpAdInfo: TPAdInfo?) {
                                logAd(TAG, "Banner ad loaded successfully")
                                isAdLoaded = true
                            }

                            override fun onAdLoadFailed(error: TPAdError?) {
                                logAd(TAG, "Banner ad failed to load: ${error?.errorMsg}")
                                isAdLoaded = false
                            }

                            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                                logAd(TAG, "Banner ad impression")
                                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "banner")
                            }

                            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                                logAd(TAG, "Banner ad clicked")
                            }

                            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                                logAd(TAG, "Banner ad closed")
                            }
                        })

                        loadAd(adUnitId)
                    }
                    FrameLayout(context).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        addView(banner)
                    }
                },
                update = { banner ->
                    // Nothing to update
                }
            )
        }
    }
}
