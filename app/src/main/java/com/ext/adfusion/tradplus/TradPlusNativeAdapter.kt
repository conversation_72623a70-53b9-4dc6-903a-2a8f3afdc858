package com.ext.adfusion.tradplus

import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.ext.adfusion.interfaces.NativeAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.example.clean0522.R
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.nativead.NativeAdListener
import com.tradplus.ads.open.nativead.TPNative

private const val TAG = "TradPlusNativeAd"

class TradPlusNativeAdapter(private val context: Context) : NativeAdAdapter {

    private var tpNative: TPNative? = null
    private var isLoading = false
    private var isAdRendered = false
    private var adLoadCallback: ((Boolean) -> Unit)? = null

    override fun loadAd(adUnitId: String) {
        loadAd(adUnitId, null)
    }

    override fun loadAd(adUnitId: String, callback: ((Boolean) -> Unit)?) {
        if (isLoading) {
            return
        }

        isLoading = true
        adLoadCallback = callback
        
        // Destroy previous ad if exists
        tpNative = null
        
        // Create TPNative - requires Activity context for proper functionality
        val activity = context as? Activity
        if (activity == null) {
            logAd(TAG, "Context is not an Activity, native ad may not work properly")
            isLoading = false
            adLoadCallback?.invoke(false)
            return
        }

        tpNative = TPNative(activity, adUnitId)
        tpNative?.setAdListener(object : NativeAdListener() {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {
                logAd(TAG, "Native ad loaded")
                AdEventReporter.analysisPostEvent("tradplus_native_loaded")
                isLoading = false
                adLoadCallback?.invoke(true)
            }

            override fun onAdLoadFailed(error: TPAdError?) {
                logAd(TAG, "Native ad failed to load: ${error?.errorMsg}")
                AdEventReporter.analysisPostEvent("tradplus_native_load_fail")
                isLoading = false
                adLoadCallback?.invoke(false)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Native ad clicked")
                AdEventReporter.analysisPostEvent("tradplus_native_click")
            }

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Native ad impression")
                AdEventReporter.analysisPostEvent("tradplus_native_impress")
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "native")
            }

            override fun onAdShowFailed(error: TPAdError?, tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Native ad show failed: ${error?.errorMsg}")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Native ad closed")
            }
        })

        tpNative?.loadAd()
        logAd(TAG, "Native ad loading...")
        AdEventReporter.analysisPostEvent("tradplus_native_loading")
    }

    override fun isAdLoaded(): Boolean {
        return tpNative?.isReady == true
    }

    override fun renderAd(container: ViewGroup, adUnitId: String) {
        val activity = context as? Activity
        if (activity == null) {
            logAd(TAG, "Context is not an Activity, cannot render ad.")
            return
        }

        val currentNative = tpNative
        if (currentNative == null || !currentNative.isReady) {
            logAd(TAG, "Ad not loaded, attempting to load.")
            if (!isLoading) {
                loadAd(adUnitId)
            }
            return
        }

        container.removeAllViews()

        // Use TRADPlus standard layout file
        val layoutId = R.layout.tp_native_ad_list_item
        
        try {
            currentNative.showAd(container, layoutId)
            isAdRendered = true
            logAd(TAG, "TRADPlus native ad rendered")
        } catch (e: Exception) {
            logAd(TAG, "Failed to render native ad: ${e.message}")
        }
    }

    @Composable
    override fun NativeAdView(adUnitId: String, modifier: Modifier, showPadding: Boolean) {
        val isLoaded = remember { mutableStateOf(isAdLoaded()) }
        var showLoading by remember { mutableStateOf(true) }

        val composition by rememberLottieComposition(
            LottieCompositionSpec.Asset("ad/banner_large.json")
        )

        val progress by animateLottieCompositionAsState(
            composition = composition,
            iterations = LottieConstants.IterateForever
        )

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> {
                        destroy()
                        logAd(TAG, "Native ad destroyed")
                    }
                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        Box(
            modifier = modifier.fillMaxWidth()
                .heightIn(max = 140.dp)
                .background(
                    color = colorResource(R.color.show_ad_bg),
                    shape = RoundedCornerShape(4.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            // Show Lottie animation while ad is loading
            if (showLoading) {
                LottieAnimation(
                    composition = composition,
                    progress = { progress },
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                        .graphicsLayer(scaleX = 1.4f)
                )
            }

            // AndroidView for the actual ad
            AndroidView(
                factory = { context ->
                    FrameLayout(context).apply {
                        if (!isAdLoaded()) {
                            loadAd(adUnitId) { success ->
                                isLoaded.value = success
                                if (success) {
                                    showLoading = false
                                    renderAd(this, adUnitId)
                                }
                            }
                        } else {
                            showLoading = false
                            renderAd(this, adUnitId)
                        }
                    }
                },
                update = { container ->
                },
                modifier = modifier.fillMaxWidth()
            )
        }
    }

    override fun destroy() {
        tpNative = null
        logAd(TAG, "TRADPlus native destroy")
    }
}
