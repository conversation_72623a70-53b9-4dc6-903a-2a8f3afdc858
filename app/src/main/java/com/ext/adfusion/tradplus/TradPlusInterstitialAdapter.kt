package com.ext.adfusion.tradplus

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.adfusion.util.AdEventReporter
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.interstitial.InterstitialAdListener
import com.tradplus.ads.open.interstitial.TPInterstitial

private const val TAG = "TradPlusInterstitialAd"

/**
 * TRADPlus implementation of InterstitialAdAdapter
 */
class TradPlusInterstitialAdapter private constructor() : InterstitialAdAdapter {

    private var tpInterstitial: TPInterstitial? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 50 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false
    private var currentAdUnitId: String? = null

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded() && currentAdUnitId == adUnitId) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 50 minutes), loading new ad")
                tpInterstitial = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        // Create new interstitial ad
        // Note: TPInterstitial may require an Activity context for some ad networks
        tpInterstitial = TPInterstitial(null, adUnitId)

        tpInterstitial?.setAdListener(object : InterstitialAdListener {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad loaded")
                isLoading = false
                isAdShown = false
                adLoadedTimestamp = System.currentTimeMillis()
                adListener?.onAdLoaded()
            }

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad displayed")
                adListener?.onAdDisplay()
                isAdShown = true
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "interstitial")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad hidden")
                adListener?.onAdClosed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdFailed(error: TPAdError?) {
                logAd(TAG, "Interstitial ad failed to load: ${error?.errorMsg}")
                isLoading = false
                adListener?.onAdFailedToLoad()
            }

            override fun onAdVideoStart(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad video started")
            }

            override fun onAdVideoEnd(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Interstitial ad video ended")
            }

            override fun onAdVideoError(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                logAd(TAG, "Interstitial ad video error: ${error?.errorMsg}")
            }
        })

        tpInterstitial?.loadAd()
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (tpInterstitial == null || tpInterstitial?.isReady() != true) {
            logAd(TAG, "The interstitial ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        // Set a temporary listener for this ad display
        tpInterstitial?.setAdListener(object : InterstitialAdListener {
            override fun onAdLoaded(tpAdInfo: TPAdInfo?) {}

            override fun onAdImpression(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Ad showed fullscreen content.")
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                adListener?.onAdDisplay()
                isAdShown = true
                AdEventReporter.postPaidEventByTradPlus(tpAdInfo, "interstitial")
            }

            override fun onAdClosed(tpAdInfo: TPAdInfo?) {
                logAd(TAG, "Ad was dismissed.")
                onAdDismissed()
                adListener?.onAdClosed()
                // Don't reload ad here - follow Admob pattern
            }

            override fun onAdClicked(tpAdInfo: TPAdInfo?) {
                adListener?.onAdClick()
            }

            override fun onAdFailed(error: TPAdError?) {}

            override fun onAdVideoStart(tpAdInfo: TPAdInfo?) {}

            override fun onAdVideoEnd(tpAdInfo: TPAdInfo?) {}

            override fun onAdVideoError(tpAdInfo: TPAdInfo?, error: TPAdError?) {
                logAd(TAG, "Ad failed to show: ${error?.errorMsg}")
                onAdFailedToShow()
            }
        })

        tpInterstitial?.showAd(activity, null)
    }

    override fun isAdLoaded(): Boolean {
        return tpInterstitial?.isReady() == true
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            tpInterstitial = null
            isAdShown = false
            instance = null
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                tpInterstitial = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            tpInterstitial = null
            isAdShown = false
            instance = null
            isLoading = false
        }
    }

    companion object {
        private const val TAG = "TradPlusInterstitialAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: TradPlusInterstitialAdapter? = null

        fun getInstance(): TradPlusInterstitialAdapter {
            return instance ?: synchronized(this) {
                instance ?: TradPlusInterstitialAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getIntersId())
        }
    }
}
