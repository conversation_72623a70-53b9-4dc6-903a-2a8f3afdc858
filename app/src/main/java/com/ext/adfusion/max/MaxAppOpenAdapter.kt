package com.ext.adfusion.max

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AppOpenAdAdapter
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.adfusion.util.AdEventReporter

/**
 * MAX (AppLovin) implementation of AppOpenAdProvider
 */
class MaxAppOpenAdapter private constructor() : AppOpenAdAdapter {

    private var appOpenAd: com.applovin.mediation.ads.MaxAppOpenAd? = null
    private var currentAdUnitId: String? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 55 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 55 minutes), loading new ad")
                appOpenAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        // Create a new app open ad if needed
        if (appOpenAd == null) {
            appOpenAd = com.applovin.mediation.ads.MaxAppOpenAd(adUnitId)

            appOpenAd?.setListener(object : MaxAdListener {
                override fun onAdLoaded(ad: MaxAd) {
                    logAd(TAG, "App open ad loaded")
                    isLoading = false
                    adLoadedTimestamp = System.currentTimeMillis()
                    isAdShown = false
                    adListener?.onAdLoaded()
                }

                override fun onAdDisplayed(ad: MaxAd) {
                    logAd(TAG, "App open ad displayed")
                    isAdShown = true
                    adListener?.onAdDisplay()
                }

                override fun onAdHidden(ad: MaxAd) {
                    logAd(TAG, "App open ad hidden")
                    // Load the next ad
                    onDestroy()
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadCached()
                    }, 1000)
                }

                override fun onAdClicked(ad: MaxAd) {
                    logAd(TAG, "App open ad clicked")
                    adListener?.onAdClick()
                }

                override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                    logAd(TAG, "App open ad failed to load: ${error.message}")
                    isLoading = false
                    adListener?.onAdFailedToLoad()
                }

                override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                    logAd(TAG, "App open ad failed to display: ${error.message}")
                    // Retry loading the ad
                    onDestroy()
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadCached()
                    }, 1000)
                }
            })

            appOpenAd?.setRevenueListener {maxAd ->
                AdEventReporter.postPaidEventByMax(maxAd)
            }
        }

        appOpenAd?.loadAd()
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (!isAdLoaded()) {
            logAd(TAG, "The app open ad is not ready yet.")
            onAdFailedToShow()

            // Try to load a new ad
            currentAdUnitId?.let { loadAd(it) }
            return
        }

        // Set a listener for the ad dismiss event
        appOpenAd?.setListener(object : MaxAdListener {
            override fun onAdLoaded(ad: MaxAd) {}

            override fun onAdDisplayed(ad: MaxAd) {
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                adListener?.onAdDisplay()
            }

            override fun onAdHidden(ad: MaxAd) {
                onAdDismissed()
                // Load the next ad
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(ad: MaxAd) {
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {}

            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                onAdFailedToShow()
                // Retry loading the ad
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }
        })

        appOpenAd?.showAd()
    }

    override fun isAdLoaded(): Boolean {
        if (appOpenAd?.isReady != true) return false

        val currentTime = System.currentTimeMillis()
        val adAge = currentTime - adLoadedTimestamp
        return adAge < AD_EXPIRY_TIME
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            appOpenAd = null
            instance = null
            isAdShown = false
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                appOpenAd = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            appOpenAd = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        private const val TAG = "MaxAppOpenAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: MaxAppOpenAdapter? = null

        fun getInstance(): MaxAppOpenAdapter {
            return instance ?: synchronized(this) {
                MaxAppOpenAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getAppOpenId())
        }
    }
}