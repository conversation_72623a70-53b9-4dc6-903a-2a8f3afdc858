package com.ext.adfusion.max

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.MaxReward
import com.applovin.mediation.MaxRewardedAdListener
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.RewardedAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter

private const val TAG = "MaxRewardedAd"

/**
 * MAX (AppLovin) implementation of RewardedAdAdapter
 */
class MaxRewardedAdapter private constructor() : RewardedAdAdapter {

    private var rewardedAd: com.applovin.mediation.ads.MaxRewardedAd? = null
    private var currentAdUnitId: String? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 55 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME && currentAdUnitId == adUnitId) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 55 minutes), loading new ad")
                rewardedAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        if (rewardedAd == null) {
            rewardedAd = com.applovin.mediation.ads.MaxRewardedAd.getInstance(adUnitId)



            rewardedAd?.setListener(object : MaxRewardedAdListener {
                override fun onAdLoaded(ad: MaxAd) {
                    logAd(TAG, "Rewarded ad loaded")
                    isLoading = false
                    isAdShown = false
                    adLoadedTimestamp = System.currentTimeMillis()
                    adListener?.onAdLoaded()
                }

                override fun onAdDisplayed(ad: MaxAd) {
                    logAd(TAG, "Rewarded ad displayed")
                    isAdShown = true
                    adListener?.onAdDisplay()
                }

                override fun onAdHidden(ad: MaxAd) {
                    logAd(TAG, "Rewarded ad hidden")
                    adListener?.onAdClosed()
                    onDestroy()
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadCached()
                    }, 1000)
                }

                override fun onAdClicked(ad: MaxAd) {
                    logAd(TAG, "Rewarded ad clicked")
                    adListener?.onAdClick()
                }

                override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                    logAd(TAG, "Rewarded ad failed to load: ${error.message}")
                    isLoading = false
                    adListener?.onAdFailedToLoad()
                }

                override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                    logAd(TAG, "Rewarded ad failed to display: ${error.message}")
                }

                override fun onUserRewarded(ad: MaxAd, reward: MaxReward) {
                    logAd(TAG, "User rewarded: ${reward.amount} ${reward.label}")
                    adListener?.onRewarded()
                }
            })

            rewardedAd?.setRevenueListener {maxAd ->
                AdEventReporter.postPaidEventByMax(maxAd)
            }
        }

        rewardedAd?.loadAd()
    }

    override fun showAd(
        activity: Activity,
        onRewarded: (amount: Int) -> Unit,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (rewardedAd == null || rewardedAd?.isReady != true) {
            logAd(TAG, "The rewarded ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        rewardedAd?.setListener(object : MaxRewardedAdListener {
            override fun onAdLoaded(ad: MaxAd) {
            }

            override fun onAdDisplayed(ad: MaxAd) {
                logAd(TAG, "Rewarded ad displayed")
                isAdShown = true
                adListener?.onAdDisplay()
            }

            override fun onAdHidden(ad: MaxAd) {
                logAd(TAG, "Rewarded ad hidden")
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdClicked(ad: MaxAd) {
                logAd(TAG, "Rewarded ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
            }

            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                logAd(TAG, "Rewarded ad failed to display: ${error.message}")
            }

            override fun onUserRewarded(ad: MaxAd, reward: MaxReward) {
                logAd(TAG, "User rewarded: ${reward.amount} ${reward.label}")
                adListener?.onRewarded()
            }
        })

        rewardedAd?.showAd(activity)
    }

    override fun isAdLoaded(): Boolean {
        return rewardedAd?.isReady == true
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            rewardedAd = null
            instance = null
            isAdShown = false
            return
        }

        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                rewardedAd = null
                isAdShown = false
                instance = null
                return
            }
        }

        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            rewardedAd = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: MaxRewardedAdapter? = null

        fun getInstance(): MaxRewardedAdapter {
            return instance ?: synchronized(this) {
                MaxRewardedAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getRewardedId())
        }
    }
}