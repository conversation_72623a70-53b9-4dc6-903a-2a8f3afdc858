package com.ext.adfusion.max

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.adfusion.util.AdEventReporter

private const val TAG = "MaxInterstitialAd"

/**
 * MAX (AppLovin) implementation of InterstitialAdProvider
 */
class MaxInterstitialAdapter private constructor() : InterstitialAdAdapter {

    private var interstitialAd: com.applovin.mediation.ads.MaxInterstitialAd? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 55 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false
    private var currentAdUnitId: String? = null

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded() && currentAdUnitId == adUnitId) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 55 minutes), loading new ad")
                interstitialAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        currentAdUnitId = adUnitId

        // Create new interstitial ad
        interstitialAd = com.applovin.mediation.ads.MaxInterstitialAd(adUnitId)

        interstitialAd?.setListener(object : MaxAdListener {
            override fun onAdLoaded(ad: MaxAd) {
                logAd(TAG, "Interstitial ad loaded")
                isLoading = false
                isAdShown = false
                adLoadedTimestamp = System.currentTimeMillis()
                adListener?.onAdLoaded()
            }

            override fun onAdDisplayed(ad: MaxAd) {
                logAd(TAG, "Interstitial ad displayed")
                adListener?.onAdDisplay()
                isAdShown = true
            }

            override fun onAdHidden(ad: MaxAd) {
                logAd(TAG, "Interstitial ad hidden")
                adListener?.onAdClosed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                },1000)
            }

            override fun onAdClicked(ad: MaxAd) {
                logAd(TAG, "Interstitial ad clicked")
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                logAd(TAG, "Interstitial ad failed to load: ${error.message}")
                isLoading = false
                adListener?.onAdFailedToLoad()
            }

            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                logAd(TAG, "Interstitial ad failed to display: ${error.message}")
            }
        })

        interstitialAd?.loadAd()
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (interstitialAd == null || interstitialAd?.isReady != true) {
            logAd(TAG, "The interstitial ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        // Set a temporary listener for this ad display
        interstitialAd?.setListener(object : MaxAdListener {
            override fun onAdLoaded(ad: MaxAd) {}

            override fun onAdDisplayed(ad: MaxAd) {
                logAd(TAG, "Ad showed fullscreen content.")
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                adListener?.onAdDisplay()
                isAdShown = true
            }

            override fun onAdHidden(ad: MaxAd) {
                logAd(TAG, "Ad was dismissed.")
                onAdDismissed()
                adListener?.onAdClosed()
                // Don't reload ad here - follow Admob pattern
            }

            override fun onAdClicked(ad: MaxAd) {
                adListener?.onAdClick()
            }

            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {}

            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                logAd(TAG, "Ad failed to show: ${error.message}")
                onAdFailedToShow()
            }
        })

        interstitialAd?.setRevenueListener {maxAd ->
            AdEventReporter.postPaidEventByMax(maxAd)
        }

        interstitialAd?.showAd()
    }

    override fun isAdLoaded(): Boolean {
        return interstitialAd?.isReady == true
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            interstitialAd = null
            isAdShown = false
            instance = null
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                interstitialAd = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            interstitialAd = null
            isAdShown = false
            instance = null
            isLoading = false
        }
    }

    companion object {
        private const val TAG = "MaxInterstitialAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: MaxInterstitialAdapter? = null

        fun getInstance(): MaxInterstitialAdapter {
            return instance ?: synchronized(this) {
                instance ?: MaxInterstitialAdapter().also { instance = it }
            }
        }

        fun loadCached() {
            getInstance().loadAd(AdFusionConfig.getIntersId())
        }
    }
}