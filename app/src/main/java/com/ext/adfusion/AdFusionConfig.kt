package com.ext.adfusion

import com.example.clean0522.data.manager.ComPreferencesManager
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.remoteset.ConfigManager
import kotlin.math.abs

/**
 * Configuration class for ad unit IDs and settings
 */
object AdFusionConfig {
    // AdMob Test Ad Unit IDs
    object AdMob {
        // Test IDs for development
        const val TEST_BANNER = "ca-app-pub-3940256099942544/6300978111"
        const val TEST_INTERSTITIAL = "ca-app-pub-3940256099942544/1033173712"
        const val TEST_REWARDED = "ca-app-pub-3940256099942544/5224354917"
        const val TEST_NATIVE_ADVANCED = "ca-app-pub-3940256099942544/2247696110"
        const val TEST_APP_OPEN = "ca-app-pub-3940256099942544/9257395921"

        // Production IDs - Replace with your real ad unit IDs
        const val BANNER = TEST_BANNER
        const val INTERSTITIAL = TEST_INTERSTITIAL
        const val INTERSTITIAL_TIME = TEST_INTERSTITIAL
        const val REWARDED = TEST_REWARDED
        const val NATIVE = TEST_NATIVE_ADVANCED
        const val APP_OPEN = TEST_APP_OPEN
    }

    object TRAD {
        // Test IDs or placeholder IDs - Replace with your real ad unit IDs
        const val BANNER = "A24091715B4FCD50C0F2039A5AF7C4BB"//"feeb255bdcd6a42d"
        const val INTERSTITIAL = "E609A0A67AF53299F2176C3A7783C46D"
        const val INTERSTITIAL_TIME = "E609A0A67AF53299F2176C3A7783C46D"//"2c74c7da96214bd5"
        const val REWARDED = "39DAC7EAC046676C5404004A311D1DB1"
        const val NATIVE = "DDBF26FBDA47FBE2765F1A089F1356BF"
        const val APP_OPEN = "D9118E91DD06DF6D322369455CAED618"//"104f1385415d8a80"
    }

    const val INTERSTITIAL_INTERVAL_SEC = 90
    const val INTERSTITIAL_OUT_TIME = 8L
    const val APPOPEN_OUT_TIME = 8L
    const val REWARDED_OUT_TIME = 8L

    const val INTERSTITIAL_RP_TIME = -1

    fun getIntersId(): String {
        return if (ConfigManager.getAdSettings().rpInterstitial >= 0) {
            if ((System.currentTimeMillis() - ComPreferencesManager.instance.getRegisterTime()) >= ConfigManager.getAdSettings().rpInterstitial * 60 * 1000) {
                if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradInterstitialTimeKey else ConfigManager.getAdKeys().admobInterstitialTimeKey
            } else {
                if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradInterstitialKey else ConfigManager.getAdKeys().admobInterstitialKey
            }
        } else {
            if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradInterstitialKey else ConfigManager.getAdKeys().admobInterstitialKey
        }
    }

    fun getBannerId(): String {
        return if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradBannerKey else ConfigManager.getAdKeys().admobBannerKey
    }

    fun getAppOpenId(): String {
        return if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradOpenKey else ConfigManager.getAdKeys().admobOpenKey
    }

    fun getRewardedId(): String {
        return if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradRewardKey else ConfigManager.getAdKeys().admobRewardKey
    }

    fun getNativeId(): String {
        return if (ConfigManager.getAdKeys().isTrad) ConfigManager.getAdKeys().tradNativeKey else ConfigManager.getAdKeys().admobNativeKey
    }

    fun allowShowAd(): Boolean {
        val interval = abs(System.currentTimeMillis() - AdFusionPreferences.instance.getAdShowLastTime())
        return interval >= ConfigManager.getAdSettings().sepInterstitial * 1000
    }

    fun openNative(): Boolean{
        return ConfigManager.getAdSettings().switchNative
    }

    fun openBanner(): Boolean{
        return ConfigManager.getAdSettings().switchBanner
    }

    val intersLoadTime = ConfigManager.getAdSettings().interstitialWait
    val openLoadTime = ConfigManager.getAdSettings().openWait
    val rewardLoadTime = ConfigManager.getAdSettings().waitReward
}