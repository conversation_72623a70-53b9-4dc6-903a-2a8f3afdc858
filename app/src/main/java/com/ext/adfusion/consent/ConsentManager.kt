package com.ext.adfusion.consent

import android.app.Activity
import android.content.Context
import com.ext.adfusion.util.AdEventReporter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.google.android.ump.ConsentDebugSettings
import com.google.android.ump.ConsentForm
import com.google.android.ump.ConsentInformation
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.FormError
import com.google.android.ump.UserMessagingPlatform

/**
 * Manages user consent for personalized ads using Google's User Messaging Platform (UMP)
 */
object ConsentManager {
    private const val TAG = "ConsentManager"
    private lateinit var consentInformation: ConsentInformation
    private var consentForm: ConsentForm? = null

    // Consent status values
    const val CONSENT_UNKNOWN = 0
    const val CONSENT_REQUIRED = 1
    const val CONSENT_OBTAINED = 2
    const val CONSENT_NOT_REQUIRED = 3

    /**
     * Initializes the consent information
     *
     * @param context Application context
     * @param debugGeography Optional debug geography setting
     * @param testDeviceHashedIds Optional list of test device IDs for testing
     * @return The initialized ConsentInformation instance
     */
    fun initialize(
        context: Context,
        debugGeography: Int? = null,
        testDeviceHashedIds: List<String>? = null
    ): ConsentInformation {
        consentInformation = UserMessagingPlatform.getConsentInformation(context)
        return consentInformation
    }

    /**
     * Builds the ConsentRequestParameters
     *
     * @param context Application context
     * @param debugGeography Optional debug geography setting
     * @param testDeviceHashedIds Optional list of test device IDs for testing
     * @return The ConsentRequestParameters object
     */
    private fun buildConsentRequestParameters(
        context: Context,
        debugGeography: Int?,
        testDeviceHashedIds: List<String>?
    ): ConsentRequestParameters {
        val paramsBuilder = ConsentRequestParameters.Builder()

        // Add debug settings if in debug mode
        if (debugGeography != null || !testDeviceHashedIds.isNullOrEmpty()) {
            val debugSettingsBuilder = ConsentDebugSettings.Builder(context)

            debugGeography?.let {
                debugSettingsBuilder.setDebugGeography(it)
            }

            if (!testDeviceHashedIds.isNullOrEmpty()) {
                for (deviceId in testDeviceHashedIds) {
                    debugSettingsBuilder.addTestDeviceHashedId(deviceId)
                }
            }

            paramsBuilder.setConsentDebugSettings(debugSettingsBuilder.build())
        }

        return paramsBuilder.build()
    }

    /**
     * Requests consent information update
     *
     * @param context Application context
     * @param debugGeography Optional debug geography setting
     * @param testDeviceHashedIds Optional list of test device IDs for testing
     * @param onConsentStatusChanged Callback with the consent status code
     */
    fun requestConsentInfoUpdate(
        context: Context,
        debugGeography: Int? = null,
        testDeviceHashedIds: List<String>? = null,
        onConsentStatusChanged: (Int) -> Unit
    ) {
        val consentInformation = initialize(context, debugGeography, testDeviceHashedIds)

        consentInformation.requestConsentInfoUpdate(
            (context as? Activity) ?: return,
            buildConsentRequestParameters(context, debugGeography, testDeviceHashedIds),
            {
                // Successfully updated consent info
                val consentStatus = getConsentStatus()
                logAd(TAG, "Consent status: $consentStatus")
                onConsentStatusChanged(consentStatus)

                if (consentStatus == CONSENT_REQUIRED && consentInformation.isConsentFormAvailable) {
                    loadConsentForm(context) { formError ->
                        formError?.let {
                            logAd(TAG, "Consent form error: ${it.message}")
                        }
                    }
                }
                AdEventReporter.analysisPostEvent("ump_req_success")
            },
            { formError ->
                // Error updating consent info
                logAd(TAG, "Error updating consent: ${formError.message}")
                onConsentStatusChanged(CONSENT_UNKNOWN)
                AdEventReporter.analysisPostEvent("ump_req_err")
            }
        )
    }

    /**
     * Loads the consent form
     *
     * @param context Application context
     * @param onFormLoaded Callback when form is loaded or fails to load
     */
    fun loadConsentForm(context: Context, onFormLoaded: (FormError?) -> Unit) {
        UserMessagingPlatform.loadConsentForm(
            context,
            { form ->
                consentForm = form
                onFormLoaded(null)
                AdEventReporter.analysisPostEvent("ump_form_loaded")
            },
            { formError ->
                consentForm = null
                onFormLoaded(formError)
                AdEventReporter.analysisPostEvent("ump_form_err")
            }
        )
    }

    /**
     * Shows the consent form if available
     *
     * @param activity Activity context
     * @param onConsentFormDismissed Callback when the form is dismissed with the updated consent status
     */
    fun showConsentFormIfAvailable(activity: Activity, onConsentFormDismissed: (Int) -> Unit) {
        if (consentForm == null) {
            loadConsentForm(activity) { formError ->
                if (formError == null && consentForm != null) {
                    showConsentForm(activity, onConsentFormDismissed)
                } else {
                    logAd(TAG, "Consent form not available: ${formError?.message ?: "Unknown error"}")
                    onConsentFormDismissed(getConsentStatus())
                }
            }
        } else {
            showConsentForm(activity, onConsentFormDismissed)
        }
    }

    /**
     * Shows the consent form
     *
     * @param activity Activity context
     * @param onConsentFormDismissed Callback when the form is dismissed with the updated consent status
     */
    private fun showConsentForm(activity: Activity, onConsentFormDismissed: (Int) -> Unit) {
        consentForm?.show(activity) { formError ->
            if (formError != null) {
                logAd(TAG, "Consent form error: ${formError.message}")
            }
            onConsentFormDismissed(getConsentStatus())
        }
    }

    /**
     * Get the current consent status as an int value
     *
     * @return The consent status code
     */
    fun getConsentStatus(): Int {
        return when {
            !this::consentInformation.isInitialized -> CONSENT_UNKNOWN
            consentInformation.consentStatus == ConsentInformation.ConsentStatus.REQUIRED -> CONSENT_REQUIRED
            consentInformation.consentStatus == ConsentInformation.ConsentStatus.OBTAINED -> CONSENT_OBTAINED
            consentInformation.consentStatus == ConsentInformation.ConsentStatus.NOT_REQUIRED -> CONSENT_NOT_REQUIRED
            else -> CONSENT_UNKNOWN
        }
    }

    /**
     * Check if consent is required
     *
     * @return True if consent is required, false otherwise
     */
    fun isConsentRequired(): Boolean {
        return getConsentStatus() == CONSENT_REQUIRED
    }

    /**
     * Check if consent has been obtained
     *
     * @return True if consent has been obtained, false otherwise
     */
    fun hasConsentBeenObtained(): Boolean {
        return getConsentStatus() == CONSENT_OBTAINED || getConsentStatus() == CONSENT_NOT_REQUIRED
    }

    /**
     * Reset the consent information - use during debugging or for testing
     */
    fun resetConsent(context: Context) {
        if (this::consentInformation.isInitialized) {
            consentInformation.reset()
            initialize(context)
        }
    }
}