package com.ext.adfusion.consent

import android.app.Activity
import com.example.clean0522.BuildConfig
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.google.android.ump.ConsentDebugSettings

/**
 * Helper class to simplify UMP consent management integration
 */
object ConsentHelper {
    private const val TAG = "ConsentHelper"
    private const val CONSENT_REQUEST_INTERVAL = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

    /**
     * Check and request consent if needed. Should be called in your application's
     * SplashActivity or initial activity before initializing ad SDKs.
     *
     * @param activity Activity context
     * @param onConsentStatusChanged Callback for consent status changes
     * @param testDeviceHashedIds Optional list of test device hashed IDs for testing
     */
    fun checkAndRequestConsent(
        activity: Activity,
        onConsentStatusChanged: (Int) -> Unit,
        testDeviceHashedIds: List<String>? = null
    ) {
        // Don't show consent again if already requested recently
        if (!shouldRequestConsent()) {
            val currentStatus = ConsentManager.getConsentStatus()
            logAd(TAG, "Using cached consent status: $currentStatus")
            onConsentStatusChanged(currentStatus)
            return
        }

        val debugGeography = if (BuildConfig.DEBUG) {
            // Use EEA debug geography for testing in debug builds
            ConsentDebugSettings.DebugGeography.DEBUG_GEOGRAPHY_EEA
        } else null

        ConsentManager.requestConsentInfoUpdate(
            activity,
            debugGeography,
            testDeviceHashedIds
        ) { consentStatus ->
            // Record that we've requested consent recently
            AdFusionPreferences.instance.setAdUMPQueTime(System.currentTimeMillis())

            logAd(TAG, "Consent status updated: $consentStatus")

            if (consentStatus == ConsentManager.CONSENT_REQUIRED) {
                showConsentForm(activity) { updatedStatus ->
                    onConsentStatusChanged(updatedStatus)
                }
            } else {
                onConsentStatusChanged(consentStatus)
            }
        }
    }

    /**
     * Shows the consent form if required
     *
     * @param activity Activity context
     * @param onComplete Callback when complete with updated consent status
     */
    fun showConsentForm(activity: Activity, onComplete: (Int) -> Unit) {
        ConsentManager.showConsentFormIfAvailable(activity) { status ->
            onComplete(status)
        }
    }

    /**
     * Check if consent can be personalized based on current status
     *
     * @return true if personalized ads are allowed, false otherwise
     */
    fun canShowPersonalizedAds(): Boolean {
        return ConsentManager.hasConsentBeenObtained()
    }

    /**
     * Determine if we should request consent based on time since last request
     *
     * @return true if consent should be requested, false otherwise
     */
    private fun shouldRequestConsent(): Boolean {
        val lastRequestTime = AdFusionPreferences.instance.getAdUMPQueTime()
        return lastRequestTime == 0L ||
                System.currentTimeMillis() - lastRequestTime > CONSENT_REQUEST_INTERVAL
    }

}