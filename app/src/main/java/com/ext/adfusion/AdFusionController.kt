package com.ext.adfusion

import android.app.Activity
import android.content.Context
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinSdk
import com.applovin.sdk.AppLovinSdkInitializationConfiguration
import com.example.clean0522.CleanApp
import com.ext.adfusion.admob.AdmobAppOpenAdapter
import com.ext.adfusion.admob.AdmobBannerAdapter
import com.ext.adfusion.admob.AdmobInterstitialAdapter
import com.ext.adfusion.admob.AdmobNativeAdapter
import com.ext.adfusion.admob.AdmobRewardedAdapter
import com.ext.adfusion.consent.ConsentHelper
import com.ext.adfusion.interfaces.AppOpenAdAdapter
import com.ext.adfusion.interfaces.BannerAdAdapter
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.interfaces.NativeAdAdapter
import com.ext.adfusion.interfaces.RewardedAdAdapter
import com.ext.adfusion.max.MaxAppOpenAdapter
import com.ext.adfusion.max.MaxBannerAdapter
import com.ext.adfusion.max.MaxInterstitialAdapter
import com.ext.adfusion.max.MaxNativeAdapter
import com.ext.adfusion.max.MaxRewardedAdapter
import com.ext.adfusion.util.AdEventManager
import com.ext.adfusion.util.AdEventReporter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.RequestConfiguration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume

/**
 * Main ad manager class that handles platform switching and initialization
 */
object AdFusionController {
    private var currentProviderType: AdProviderType = AdProviderType.ADMOB
    private val mutex = Mutex()
    const val sdk_key = ""
    var tradplus = false
    var admobInit = false
    var isRunning = false
    private var consentProcessed = false

    suspend fun initAdSdk(context: Context, isMax: Boolean) = mutex.withLock {
        suspendCancellableCoroutine { cancellableContinuation ->
            AdEventReporter.analysisPostEvent("sdk_enter")
            if (isRunning) return@suspendCancellableCoroutine
            isRunning = true
            AdEventReporter.analysisPostEvent("sdk_start")
            if (!isMax) {
                if (!admobInit){
                    MobileAds.initialize(context) {
                        admobInit = true
                        switchProviderType(false)
                        cancellableContinuation.resume(Unit)
                        isRunning = false
                        logAd("AdManager", "AdMobile SDK initialized successfully")
                        AdEventReporter.analysisPostEvent("mobile_init")
                        AdEventManager.triggerAdInit()
                    }
                }else{
                    cancellableContinuation.resume(Unit)
                    isRunning = false
                }
            }else{
                if (!tradplus){
                    logAd("AdManager", "Max SDK start")
                    val key = ""
                    if (key.isNotBlank()){
                        val initConfig = AppLovinSdkInitializationConfiguration.builder(key)
                            .setMediationProvider(AppLovinMediationProvider.MAX)
                            .build()
                        AppLovinSdk.getInstance( context ).showMediationDebugger()
                        AppLovinSdk.getInstance(context).initialize(initConfig) {
                            tradplus = true
                            switchProviderType(true)
                            cancellableContinuation.resume(Unit)
                            isRunning = false
                            logAd("AdManager", "Max SDK initialized successfully")
                            AdEventReporter.analysisPostEvent("max_init")
                            AdEventManager.triggerAdInit()
                        }
                    }else{
                        logAd("AdManager", "Max SDK key is blank")
                        cancellableContinuation.resume(Unit)
                        isRunning = false
                    }
                }else{
                    cancellableContinuation.resume(Unit)
                    isRunning = false
                }
            }
        }
    }

    /**
     * Initialize ad manager with consent check
     * Should be called from your SplashActivity before loading ads
     *
     * @param activity Activity context
     * @param isMax Whether to use MAX ad platform
     * @param onComplete Callback when initialization is complete
     */
    fun initWithConsent(activity: Activity, isMax: Boolean, onComplete: () -> Unit) {
        if (consentProcessed) {
            onComplete()
            return
        }

        // Check and request consent first
        ConsentHelper.checkAndRequestConsent(
            activity,
            { consentStatus ->
                // Apply GDPR settings to ad requests
//                applyConsent()

                consentProcessed = true

                // Initialize ad SDK after consent is handled
                GlobalScope.launch {
                    initAdSdk(activity, isMax)
                    withContext(Dispatchers.Main) {
                        onComplete()
                    }
                }
            },
            null // No test device IDs in production
        )
    }

    /**
     * Apply consent settings to ad requests
     */
    private fun applyConsent() {
        val canShowPersonalizedAds = ConsentHelper.canShowPersonalizedAds()
        logAd("AdManager", "Setting personalized ads: $canShowPersonalizedAds")

        // For AdMob/Google Mobile Ads
        val requestConfigBuilder = RequestConfiguration.Builder()
        // Set non-personalized ads if consent not given
        if (!canShowPersonalizedAds) {
            requestConfigBuilder.setTagForChildDirectedTreatment(
                RequestConfiguration.TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE
            )
            requestConfigBuilder.setTagForUnderAgeOfConsent(
                RequestConfiguration.TAG_FOR_UNDER_AGE_OF_CONSENT_TRUE
            )
            requestConfigBuilder.setMaxAdContentRating(RequestConfiguration.MAX_AD_CONTENT_RATING_G)
        }
        MobileAds.setRequestConfiguration(requestConfigBuilder.build())

        // For AppLovin/MAX - add similar settings if needed
        // AppLovinSdk.getInstance(context).settings.setHasUserConsent(canShowPersonalizedAds)
    }

    fun sdkIsInit(): Boolean {
        return tradplus || admobInit
    }

    /**
     * Switch between ad platforms
     */
    fun switchProviderType(isMax: Boolean) {
        currentProviderType = if (isMax) AdProviderType.TRADPLUS else AdProviderType.ADMOB
    }

    fun isMaxProvider(): Boolean {
        return currentProviderType == AdProviderType.TRADPLUS
    }
    
    /**
     * Get current ad platform
     */
    fun getCurrentProviderType(): AdProviderType = currentProviderType
    
    // Getter methods to access specific ad types
    
    fun getInterstitialAd(context: Context): InterstitialAdAdapter = if (getCurrentProviderType() == AdProviderType.ADMOB)
        AdmobInterstitialAdapter.getInstance(context) else MaxInterstitialAdapter.getInstance()
    
    fun getNativeAd(context: Context = CleanApp.appContext): NativeAdAdapter = if (getCurrentProviderType() == AdProviderType.ADMOB) AdmobNativeAdapter(
        context
    ) else MaxNativeAdapter(context)
    
    fun getBannerAd(): BannerAdAdapter = if (getCurrentProviderType() == AdProviderType.ADMOB) AdmobBannerAdapter() else MaxBannerAdapter()
    
    fun getRewardedAd(context: Context): RewardedAdAdapter = if (getCurrentProviderType() == AdProviderType.ADMOB)
        AdmobRewardedAdapter.getInstance(context) else MaxRewardedAdapter.getInstance()

    fun getAppOpenAd(context: Context): AppOpenAdAdapter = if (getCurrentProviderType() == AdProviderType.ADMOB)
        AdmobAppOpenAdapter.getInstance(context) else MaxAppOpenAdapter.getInstance()
}