package com.ext.adfusion.interfaces

import android.app.Activity

/**
 * Interface for rewarded ads
 */
interface RewardedAdAdapter {
    /**
     * Load a rewarded ad
     * @param adUnitId The ad unit ID
     */
    fun loadAd(adUnitId: String = com.ext.adfusion.AdFusionConfig.getRewardedId())

    /**
     * Show the rewarded ad if it's loaded
     * @param activity The activity context
     * @param onRewarded Callback when user earns a reward
     * @param onAdDismissed Callback when ad is dismissed
     * @param onAdFailedToShow Callback when ad failed to show
     */
    fun showAd(
        activity: Activity,
        onRewarded: (amount: Int) -> Unit = {},
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: () -> Unit = {}
    )

    /**
     * Check if a rewarded ad is loaded and ready to be shown
     * @return true if ad is loaded, false otherwise
     */
    fun isAdLoaded(): Boolean

    /**
     * Set the ad listener
     * @param adListener The ad listener
     */
    fun setAdListener(adListener: AdEventListener)

    /**
     * Clean up resources
     */
    fun onDestroy()
}