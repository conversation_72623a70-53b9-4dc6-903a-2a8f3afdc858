package com.ext.adfusion.interfaces

import android.app.Activity

/**
 * Interface for app open ads
 */
interface AppOpenAdAdapter {
    /**
     * Load an app open ad
     * @param adUnitId The ad unit ID
     */
    fun loadAd(adUnitId: String = com.ext.adfusion.AdFusionConfig.getAppOpenId())

    /**
     * Show the app open ad if it's loaded
     * @param activity The activity context
     * @param onAdDismissed Callback when ad is dismissed
     * @param onAdFailedToShow Callback when ad failed to show
     */
    fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: () -> Unit = {}
    )

    /**
     * Check if an app open ad is loaded and ready to be shown
     * @return true if ad is loaded, false otherwise
     */
    fun isAdLoaded(): Boolean

    /**
     * Set listener for ad events
     * @param adListener The ad listener
     */
    fun setAdListener(adListener: AdEventListener)

    /**
     * Clean up resources when the ad is no longer needed
     */
    fun onDestroy()
}