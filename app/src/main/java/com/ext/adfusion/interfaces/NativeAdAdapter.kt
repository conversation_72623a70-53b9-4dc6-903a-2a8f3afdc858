package com.ext.adfusion.interfaces

import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ext.adfusion.AdFusionConfig

/**
 * Interface for native ads
 */
interface NativeAdAdapter {
    /**
     * Load a native ad
     * @param adUnitId The ad unit ID
     */
    fun loadAd(adUnitId: String)

    /**
     * Load a native ad with callback
     * @param adUnitId The ad unit ID
     * @param callback Callback to notify when ad is loaded or failed
     */
    fun loadAd(adUnitId: String, callback: ((Boolean) -> Unit)?)

    /**
     * Check if a native ad is loaded
     * @return true if ad is loaded, false otherwise
     */
    fun isAdLoaded(): Boolean

    /**
     * Render native ad into a container view
     * @param container The ViewGroup container to render the ad into
     * @param adUnitId The ad unit ID
     */
    fun renderAd(container: ViewGroup, adUnitId: String)

    /**
     * Composable function to show native ad in Compose UI
     * @param adUnitId The ad unit ID
     * @param modifier Modifier for the composable
     */
    @Composable
    fun NativeAdView(adUnitId: String = AdFusionConfig.getNativeId(), modifier: Modifier = Modifier,showPadding: Boolean = false)

    /**
     * Destroy the ad and clean up resources
     * Should be called in the onDestroy lifecycle method
     */
    fun destroy()
}