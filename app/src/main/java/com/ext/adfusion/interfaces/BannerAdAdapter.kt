package com.ext.adfusion.interfaces

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ext.adfusion.AdFusionConfig

/**
 * Interface for banner ads
 */
interface BannerAdAdapter {
    /**
     * Load a banner ad
     * @param adUnitId The ad unit ID
     */
    fun loadAd(adUnitId: String)

    /**
     * Composable function to show banner ad in Compose UI
     * @param adUnitId The ad unit ID
     * @param modifier Modifier for the composable
     */
    @Composable
    fun BannerAdView(adUnitId: String = AdFusionConfig.getBannerId(), modifier: Modifier = Modifier)
}