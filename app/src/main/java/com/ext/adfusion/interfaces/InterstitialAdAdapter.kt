package com.ext.adfusion.interfaces

import android.app.Activity

/**
 * Interface for interstitial ads
 */
interface InterstitialAdAdapter {
    /**
     * Load an interstitial ad
     * @param adUnitId The ad unit ID
     */
    fun loadAd(adUnitId: String = com.ext.adfusion.AdFusionConfig.getIntersId())

    /**
     * Show the interstitial ad if it's loaded
     * @param activity The activity context
     * @param onAdDismissed Callback when ad is dismissed
     * @param onAdFailedToShow Callback when ad failed to show
     */
    fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: () -> Unit = {}
    )

    /**
     * Check if an interstitial ad is loaded and ready to be shown
     * @return true if ad is loaded, false otherwise
     */
    fun isAdLoaded(): Boolean

    fun setAdListener(adListener: AdEventListener)

    fun onDestroy()
}