package com.ext.adfusion.admob

import android.view.Gravity
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.applovin.sdk.AppLovinSdkUtils.dpToPx
import com.ext.adfusion.interfaces.BannerAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.example.clean0522.R

private const val TAG = "AdmobBannerAd"

class AdmobBannerAdapter() : BannerAdAdapter {

    override fun loadAd(adUnitId: String) {
    }

    @Composable
    override fun BannerAdView(adUnitId: String, modifier: Modifier) {
        var isAdLoaded by remember { mutableStateOf(false) }
        var adView by remember { mutableStateOf<AdView?>(null) }
        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        adView?.resume()
                        logAd(TAG, "Banner ad resumed")
                    }

                    Lifecycle.Event.ON_PAUSE -> {
                        adView?.pause()
                        logAd(TAG, "Banner ad paused")
                    }

                    Lifecycle.Event.ON_DESTROY -> {
                        adView?.destroy()
                        logAd(TAG, "Banner ad destroyed")
                    }

                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
                adView?.destroy()
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(70.dp)
                .background(color = colorResource(R.color.show_ad_bg))
                .padding(5.dp)
                .then(modifier),
            contentAlignment = Alignment.Center
        ) {
            if (!isAdLoaded) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset("ad/banner_small.json")
                )
                
                LottieAnimation(
                    composition = composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier.fillMaxWidth()
                        .graphicsLayer(scaleY = 1.2f)
                )
            }
            
            AndroidView(
                factory = { context ->
                    FrameLayout(context).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        addView(AdView(context).apply {
                            setAdUnitId(adUnitId)
                            layoutParams = FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            ).apply {
                                gravity = Gravity.CENTER_HORIZONTAL
                            }
                            try {
                                val adSize = AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
                                    context,
                                    context.resources.displayMetrics.widthPixels / context.resources.displayMetrics.density.toInt() - dpToPx(context, 5)
                                )
                                setAdSize(adSize)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }

                            adListener = object : AdListener() {
                                override fun onAdLoaded() {
                                    logAd(TAG, "Banner ad loaded successfully")
                                    isAdLoaded = true
                                }

                                override fun onAdFailedToLoad(error: LoadAdError) {
                                    logAd(TAG, "Banner ad failed to load: ${error.message}")
                                    isAdLoaded = false
                                }

                                override fun onAdImpression() {
                                    logAd(TAG, "Banner ad impression recorded")
                                }
                            }

                            onPaidEventListener = OnPaidEventListener { adValue ->
                                val netWork = this.responseInfo?.mediationAdapterClassName ?: ""
                                AdEventReporter.postPaidEventByAdmob(adValue, netWork, "banner")
                            }


                            loadAd(AdRequest.Builder().build())
                            adView = this
                        })
                    }
                },
                update = { adView ->
                }
            )
        }
    }
}