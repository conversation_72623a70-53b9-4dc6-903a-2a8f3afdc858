package com.ext.adfusion.admob

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.example.clean0522.CleanApp
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AdEventListener
import com.ext.adfusion.interfaces.RewardedAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback

private const val TAG = "AdmobRewardedAd"

/**
 * AdMob implementation of RewardedAdProvider
 */
class AdmobRewardedAdapter private constructor(private val context: Context) : RewardedAdAdapter {

    private var rewardedAd: RewardedAd? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 50 minutes), loading new ad")
                rewardedAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        val adRequest = AdRequest.Builder().build()

        RewardedAd.load(
            context,
            adUnitId,
            adRequest,
            object : RewardedAdLoadCallback() {
                override fun onAdFailedToLoad(adError: LoadAdError) {
                    logAd(TAG, adError.toString())
                    rewardedAd = null
                    isLoading = false
                    adListener?.onAdFailedToLoad()
                }

                override fun onAdLoaded(ad: RewardedAd) {
                    logAd(TAG, "Rewarded ad loaded $this@AdmobRewardedAd")
                    rewardedAd = ad
                    isLoading = false
                    isAdShown = false
                    logAd(TAG, "Ad loaded successfully $isAdShown")
                    adLoadedTimestamp = System.currentTimeMillis()
                    adListener?.onAdLoaded()
                }
            }
        )
    }

    override fun showAd(
        activity: Activity,
        onRewarded: (amount: Int) -> Unit,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (rewardedAd == null) {
            logAd(TAG, "The rewarded ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        rewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                logAd(TAG, "Ad was dismissed.")
                // Clear ad reference
                rewardedAd = null
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                logAd(TAG, "Ad failed to show: ${adError.message}")
                rewardedAd = null
                onAdFailedToShow()
            }

            override fun onAdShowedFullScreenContent() {
                logAd(TAG, "Ad showed fullscreen content.")
                isAdShown = true
                adListener?.onAdDisplay()
            }
        }

        rewardedAd?.onPaidEventListener = OnPaidEventListener { adValue ->
            val netWork = rewardedAd?.responseInfo?.mediationAdapterClassName ?: ""
            AdEventReporter.postPaidEventByAdmob(adValue, netWork, "rewarded")
        }

        rewardedAd?.show(activity) { rewardItem ->
            val rewardAmount = rewardItem.amount
            logAd(TAG, "User earned reward: $rewardAmount")
            adListener?.onRewarded()
            onRewarded(rewardAmount)
        }
    }

    override fun isAdLoaded(): Boolean {
        return rewardedAd != null
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference $this@AdmobRewardedAd")
            rewardedAd = null
            instance = null
            isAdShown = false
            return
        }

        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                rewardedAd = null
                isAdShown = false
                instance = null
            }
        }

        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            rewardedAd = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: AdmobRewardedAdapter? = null

        fun getInstance(context: Context): AdmobRewardedAdapter {
            return instance ?: synchronized(this) {
                AdmobRewardedAdapter(context.applicationContext).also { instance = it }
            }
        }

        fun loadCached() {
            getInstance(CleanApp.appContext).loadAd(AdFusionConfig.getRewardedId())
        }
    }
}