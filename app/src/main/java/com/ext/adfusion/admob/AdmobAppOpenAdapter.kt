package com.ext.adfusion.admob

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import com.ext.adfusion.interfaces.AppOpenAdAdapter
import com.ext.adfusion.interfaces.AdEventListener
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.appopen.AppOpenAd
import android.os.Handler
import android.os.Looper
import com.example.clean0522.CleanApp
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences
import com.ext.adfusion.util.AdEventReporter
import com.google.android.gms.ads.OnPaidEventListener

/**
 * AdMob implementation of AppOpenAdProvider
 */
class AdmobAppOpenAdapter private constructor(private val context: Context) : AppOpenAdAdapter {

    private var appOpenAd: AppOpenAd? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 55 minutes), loading new ad")
                appOpenAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        val request = AdRequest.Builder().build()

        AppOpenAd.load(
            context,
            adUnitId,
            request,
            object : AppOpenAd.AppOpenAdLoadCallback() {
                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    logAd(TAG, "App open ad failed to load: ${loadAdError.message}")
                    isLoading = false
                    appOpenAd = null
                    adListener?.onAdFailedToLoad()
                }

                override fun onAdLoaded(ad: AppOpenAd) {
                    logAd(TAG, "App open ad loaded")
                    appOpenAd = ad
                    isLoading = false
                    isAdShown = false
                    adLoadedTimestamp = System.currentTimeMillis()
                    adListener?.onAdLoaded()
                }
            }
        )
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (!isAdLoaded()) {
            logAd(TAG, "The app open ad is not ready yet.")
            onAdFailedToShow()
            return
        }

        appOpenAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                logAd(TAG, "App open ad dismissed.")
                // Clear ad reference
                appOpenAd = null
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                }, 1000)
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                logAd(TAG, "App open ad failed to show: ${adError.message}")
                // Clear ad reference
                appOpenAd = null
                onAdFailedToShow()
            }

            override fun onAdShowedFullScreenContent() {
                logAd(TAG, "App open aadValued showed fullscreen content.")
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                isAdShown = true
                adListener?.onAdDisplay()
            }
        }

        appOpenAd?.onPaidEventListener = OnPaidEventListener { adValue ->
            val netWork = appOpenAd?.responseInfo?.mediationAdapterClassName ?: ""
            AdEventReporter.postPaidEventByAdmob(adValue, netWork, "app_open")
        }

        appOpenAd?.show(activity)
    }

    override fun isAdLoaded(): Boolean {
        if (appOpenAd == null) return false

        val currentTime = System.currentTimeMillis()
        val adAge = currentTime - adLoadedTimestamp
        return adAge < AD_EXPIRY_TIME
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference")
            appOpenAd = null
            instance = null
            isAdShown = false
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                appOpenAd = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            appOpenAd = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        private const val TAG = "AdmobAppOpenAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: AdmobAppOpenAdapter? = null

        fun getInstance(context: Context): AdmobAppOpenAdapter {
            return instance ?: synchronized(this) {
                AdmobAppOpenAdapter(context.applicationContext).also { instance = it }
            }
        }

        fun loadCached() {
            getInstance(CleanApp.appContext).loadAd(AdFusionConfig.getAppOpenId())
        }
    }
}