package com.ext.adfusion.admob

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.example.clean0522.CleanApp
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.interfaces.AdEventListener
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.ext.adfusion.interfaces.InterstitialAdAdapter
import com.ext.adfusion.util.AdEventReporter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdFusionPreferences

/**
 * AdMob implementation of InterstitialAdProvider
 */
class AdmobInterstitialAdapter private constructor(private val context: Context) : InterstitialAdAdapter {

    private var interstitialAd: InterstitialAd? = null
    private var isLoading = false
    private var adListener: AdEventListener? = null
    private var adLoadedTimestamp: Long = 0L
    private val AD_EXPIRY_TIME = 50 * 60 * 1000L // 55 minutes in milliseconds
    private var loadStartTime: Long = 0L
    private val LOAD_TIMEOUT = 20 * 1000L // 20 seconds in milliseconds
    private var isAdShown = false

    override fun loadAd(adUnitId: String) {
        if (isLoading) return

        // Check if ad is already loaded and not expired
        if (isAdLoaded()) {
            val currentTime = System.currentTimeMillis()
            val adAge = currentTime - adLoadedTimestamp

            if (adAge < AD_EXPIRY_TIME) {
                logAd(TAG, "Ad already loaded and not expired, using cached ad")
                adListener?.onAdLoaded()
                return
            } else {
                logAd(TAG, "Ad expired (older than 55 minutes), loading new ad")
                interstitialAd = null
            }
        }

        isLoading = true
        loadStartTime = System.currentTimeMillis()
        val adRequest = AdRequest.Builder().build()

        InterstitialAd.load(
            context,
            adUnitId,
            adRequest,
            object : InterstitialAdLoadCallback() {
                override fun onAdFailedToLoad(adError: LoadAdError) {
                    logAd(TAG, adError.toString())
                    interstitialAd = null
                    isLoading = false
                    adListener?.onAdFailedToLoad()
                }

                override fun onAdLoaded(ad: InterstitialAd) {
                    logAd(TAG, "Interstitial ad loaded $instance")
                    interstitialAd = ad
                    isLoading = false
                    isAdShown = false
                    logAd(TAG, "Ad loaded successfully $isAdShown")
                    adLoadedTimestamp = System.currentTimeMillis()
                    adListener?.onAdLoaded()
                }
            }
        )
    }

    override fun showAd(
        activity: Activity,
        onAdDismissed: () -> Unit,
        onAdFailedToShow: () -> Unit
    ) {
        if (interstitialAd == null) {
            logAd(TAG, "The interstitial ad wasn't ready yet.")
            onAdFailedToShow()
            return
        }

        interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                logAd(TAG, "Ad was dismissed.")
                // Clear ad reference
                interstitialAd = null
                onAdDismissed()
                onDestroy()
                Handler(Looper.getMainLooper()).postDelayed({
                    loadCached()
                },1000)
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                logAd(TAG, "Ad failed to show: ${adError.message}")
                // Clear ad reference
                interstitialAd = null
                onAdFailedToShow()
            }

            override fun onAdShowedFullScreenContent() {
                logAd(TAG, "Ad showed fullscreen content.")
                AdFusionPreferences.instance.setAdShowLastTime(System.currentTimeMillis())
                isAdShown = true
                adListener?.onAdDisplay()
            }
        }

        interstitialAd?.onPaidEventListener = OnPaidEventListener { adValue ->
            val netWork = interstitialAd?.responseInfo?.mediationAdapterClassName ?: ""
            AdEventReporter.postPaidEventByAdmob(adValue, netWork, "interstitial")
        }

        interstitialAd?.show(activity)
    }

    override fun isAdLoaded(): Boolean {
        return interstitialAd != null
    }

    override fun setAdListener(adListener: AdEventListener) {
        this.adListener = adListener
    }

    override fun onDestroy() {
        val currentTime = System.currentTimeMillis()
        logAd(TAG, "onDestroy called $isAdShown")

        if (isAdShown) {
            // Ad has been shown, clear it
            logAd(TAG, "onDestroy: Ad has been shown, clearing reference $instance")
            interstitialAd = null
            instance = null
            isAdShown = false
            return
        }

        // Check if ad is expired
        if (isAdLoaded()) {
            val adAge = currentTime - adLoadedTimestamp
            if (adAge >= AD_EXPIRY_TIME) {
                logAd(TAG, "onDestroy: Ad expired, clearing reference")
                interstitialAd = null
                isAdShown = false
                instance = null
                return
            }
        }

        // Check if loading time exceeded timeout
        if (isLoading && (currentTime - loadStartTime >= LOAD_TIMEOUT)) {
            logAd(TAG, "onDestroy: Ad loading timeout (> 20s), clearing reference")
            interstitialAd = null
            isAdShown = false
            isLoading = false
            instance = null
        }
    }

    companion object {
        private const val TAG = "AdmobInterstitialAd"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: AdmobInterstitialAdapter? = null

        fun getInstance(context: Context): AdmobInterstitialAdapter {
            return instance ?: synchronized(this) {
                AdmobInterstitialAdapter(context.applicationContext).also { instance = it }
            }
        }

        fun loadCached() {
            val adUnitId = AdFusionConfig.getIntersId()
            getInstance(CleanApp.appContext).loadAd(adUnitId)
        }

    }
}