package com.ext.adfusion.admob

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import com.google.android.gms.ads.nativead.NativeAdView
import com.ext.adfusion.interfaces.NativeAdAdapter
import com.ext.adfusion.util.AdFusionLogger.logAd
import com.ext.adfusion.util.AdEventReporter
import com.example.clean0522.R

private const val TAG = "AdmobNativeAd"

/**
 * AdMob implementation of NativeAdProvider
 */
class AdmobNativeAdapter(private val context: Context) : NativeAdAdapter {

    private var nativeAd: NativeAd? = null
    private var isLoading = false
    private var adLoadCallback: ((Boolean) -> Unit)? = null

    override fun loadAd(adUnitId: String) {
        loadAd(adUnitId, null)
    }

    override fun loadAd(adUnitId: String, callback: ((Boolean) -> Unit)?) {
        if (isLoading) {
            return
        }

        isLoading = true
        adLoadCallback = callback

        val adLoader = AdLoader.Builder(context, adUnitId)
            .forNativeAd (object : NativeAd.OnNativeAdLoadedListener{
                override fun onNativeAdLoaded(ad: NativeAd) {
                    nativeAd?.destroy()
                    nativeAd = ad
                    isLoading = false
                    logAd(TAG, "Native ad loaded.")
                    AdEventReporter.analysisPostEvent("ad_native_loaded")
                    nativeAd?.setOnPaidEventListener { adValue ->
                        val netWork = ad.responseInfo?.mediationAdapterClassName ?: ""
                        AdEventReporter.postPaidEventByAdmob(adValue, netWork, "native")
                        logAd(TAG, "Native ad paid event triggered. ${adValue.valueMicros}")
                    }
                    adLoadCallback?.invoke(true)
                }
            }).withAdListener(object : AdListener() {
                override fun onAdFailedToLoad(error: LoadAdError) {
                    logAd(TAG, "Native ad failed to load: ${error.message}")
                    AdEventReporter.analysisPostEvent("ad_native_load_fail")
                    isLoading = false
                    adLoadCallback?.invoke(false)
                }

                override fun onAdClicked() {
                    logAd(TAG, "Native ad clicked.")
                    AdEventReporter.analysisPostEvent("ad_native_click")
                }

                override fun onAdImpression() {
                    logAd(TAG, "Native ad impression recorded.")
                    AdEventReporter.analysisPostEvent("ad_native_display")
                }
            })
            .withNativeAdOptions(
                NativeAdOptions.Builder()
                    .setAdChoicesPlacement(NativeAdOptions.ADCHOICES_TOP_RIGHT)
                    .build()
            )
            .build()

        adLoader.loadAd(AdRequest.Builder().build())
        AdEventReporter.analysisPostEvent("ad_native_loading")
        logAd(TAG, "Native ad loading")
    }

    override fun isAdLoaded(): Boolean {
        return nativeAd != null
    }

    override fun renderAd(container: ViewGroup, adUnitId: String) {
        if (nativeAd == null) {
            loadAd(adUnitId)
            return
        }

        // Clear any existing views
        container.removeAllViews()

        // Inflate the custom native ad layout
        val inflater = LayoutInflater.from(context)
        val adView = inflater.inflate(R.layout.layout_admob_native_ad, container, false) as NativeAdView

        // Set the media view
        val mediaView = adView.findViewById<com.google.android.gms.ads.nativead.MediaView>(R.id.ad_native_media)
        adView.mediaView = mediaView

        // Set other ad assets
        adView.headlineView = adView.findViewById(R.id.title_text_view)
        adView.bodyView = adView.findViewById(R.id.body_text_view)
        adView.callToActionView = adView.findViewById(R.id.cta_button)
        adView.iconView = adView.findViewById(R.id.icon_image_view)

        // Populate the ad data
        (adView.headlineView as? TextView)?.text = nativeAd?.headline
        (adView.bodyView as? TextView)?.text = nativeAd?.body
        (adView.callToActionView as? Button)?.text = nativeAd?.callToAction

        // App icon
        if (nativeAd?.icon != null) {
            (adView.iconView as? ImageView)?.setImageDrawable(nativeAd?.icon?.drawable)
            adView.iconView?.visibility = View.VISIBLE
        } else {
            adView.iconView?.visibility = View.GONE
        }

        // Optional fields
        if (nativeAd?.advertiser != null) {
            (adView.advertiserView as? TextView)?.text = nativeAd?.advertiser
            adView.advertiserView?.visibility = View.VISIBLE
        } else {
            adView.advertiserView?.visibility = View.GONE
        }

        adView.setNativeAd(nativeAd!!)

        container.addView(adView)
        logAd(TAG, "admob native rendered")
    }

    @Composable
    override fun NativeAdView(adUnitId: String, modifier: Modifier,showPadding: Boolean) {
        val isLoaded = remember { mutableStateOf(isAdLoaded()) }
        var showLoading by remember { mutableStateOf(true) }

        val composition by rememberLottieComposition(
            LottieCompositionSpec.Asset("ad/banner_large.json")
        )

        val progress by animateLottieCompositionAsState(
            composition = composition,
            iterations = LottieConstants.IterateForever
        )

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> {
                        destroy()
                        logAd(TAG, "Banner ad destroyed")
                    }
                    else -> {}
                }
            }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
                destroy()
            }
        }

        // Try to load ad if not loaded yet
        LaunchedEffect(key1 = Unit) {
            if (!isLoaded.value && !isLoading) {
                loadAd(adUnitId) { success ->
                    isLoaded.value = success
                    if (success) {
                        showLoading = false
                    }
                }
            }
        }

        Box(modifier = modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center) {
            Box(modifier = Modifier.fillMaxWidth().padding(vertical = if (showPadding) 12.dp else 0.dp)
                .clip(RoundedCornerShape(4.dp))){
                if (showLoading) {
                    Box(modifier = Modifier.heightIn(max = 135.dp)){
                        LottieAnimation(
                            composition = composition,
                            progress = { progress },
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.Center)
                                .graphicsLayer(scaleX = 1.45f)
                        )
                    }
                }

                // AndroidView for the actual ad
                AndroidView(
                    factory = { context ->
                        FrameLayout(context).apply {
                            if (!isAdLoaded()) {
                                loadAd(adUnitId) { success ->
                                    isLoaded.value = success
                                    if (success) {
                                        showLoading = false
                                        renderAd(this, adUnitId)
                                    }
                                }
                            } else {
                                showLoading = false
                                renderAd(this, adUnitId)
                            }
                        }
                    },
                    update = { container ->
                        val currentlyLoaded = isAdLoaded()
                        isLoaded.value = currentlyLoaded

                        if (currentlyLoaded) {
                            showLoading = false
                            renderAd(container, adUnitId)
                        }
                    },
                    modifier = modifier.fillMaxWidth()
                )
            }
        }
    }

    override fun destroy() {
        nativeAd?.destroy()
        nativeAd = null
        logAd(TAG, "admob native destroy")
    }
}