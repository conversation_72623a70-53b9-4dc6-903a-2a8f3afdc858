package com.ext.remoteset

import android.os.Parcelable
import com.example.clean0522.notify.NotifyData
import com.example.clean0522.notify.TipsShowManager
import com.ext.adfusion.AdFusionConfig
import com.ext.adfusion.AdFusionController
import kotlinx.parcelize.Parcelize

@Parcelize
data class ConfigParameters(
    var adKeys: String? = null,
    var adSettings: String? = null,
    var tipContent: String? = null,
    var tipSettings: String? = null
):Parcelable

@Parcelize
data class AdKeys(
    val isTrad: Boolean = false,
    val revenueThreshold: Float = 0.1f,
    val tradInterstitialKey: String = AdFusionConfig.TRAD.INTERSTITIAL,
    val tradInterstitialTimeKey: String = AdFusionConfig.TRAD.INTERSTITIAL_TIME,
    val tradOpenKey: String = AdFusionConfig.TRAD.APP_OPEN,
    val tradNativeKey: String = AdFusionConfig.TRAD.NATIVE,
    val tradBannerKey: String = AdFusionConfig.TRAD.BANNER,
    val tradRewardKey: String = AdFusionConfig.TRAD.REWARDED,
    val admobInterstitialKey: String = AdFusionConfig.AdMob.INTERSTITIAL,
    val admobInterstitialTimeKey: String = AdFusionConfig.AdMob.INTERSTITIAL_TIME,
    val admobOpenKey: String = AdFusionConfig.AdMob.APP_OPEN,
    val admobNativeKey: String = AdFusionConfig.AdMob.NATIVE,
    val admobBannerKey: String = AdFusionConfig.AdMob.BANNER,
    val admobRewardKey: String = AdFusionConfig.AdMob.REWARDED
):Parcelable

@Parcelize
data class AdSettings(
    val sepInterstitial: Int = AdFusionConfig.INTERSTITIAL_INTERVAL_SEC,
    val rpInterstitial: Int = AdFusionConfig.INTERSTITIAL_RP_TIME,
    val openWait: Long = AdFusionConfig.APPOPEN_OUT_TIME,
    val interstitialWait: Long = AdFusionConfig.INTERSTITIAL_OUT_TIME,
    val waitReward: Long = AdFusionConfig.REWARDED_OUT_TIME,
    val switchNative: Boolean = true,
    val switchBanner: Boolean = true
):Parcelable

@Parcelize
data class TipSettings(
    val tip_first_time: Int = TipsShowManager.TIP_FIRST_SHOW_TIME,
    val tip_next_time: Int = TipsShowManager.TIP_NEXT_SHOW_TIME,
    val tip_max_count: Int = TipsShowManager.TIP_MAX_COUNT
):Parcelable

@Parcelize
data class TipContent(
    val tip_contents: List<NotifyData> = arrayListOf()
):Parcelable