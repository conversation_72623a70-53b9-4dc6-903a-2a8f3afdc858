package com.ext.remoteset

import android.content.Context
import android.os.Build
import android.webkit.WebSettings
import com.example.clean0522.CleanApp
import com.ext.remoteset.utils.EncodeUtil
import com.ext.remoteset.utils.RemotePreferencesManager
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.gson.Gson
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.*


object AnalyticsConstants {
    const val API_BASE_URL = "http://47.253.80.156/api/"
    const val HEADER_PACKAGE_ID = "reward_no"
    const val HEADER_LANGUAGE = "lang"
    const val HEADER_VERSION_NAME = "bname"
    const val HEADER_VERSION_CODE = "bcode"
    const val HEADER_AF_ID = "appsflyer_id"
    const val HEADER_APP_INSTANCE_ID = "app_instance_id"
    const val HEADER_USER_ID = "user_id"
    const val HEADER_TIMEZONE = "timezone"

    const val EVENT_TYPE_START = "on_start"
    const val EVENT_TYPE_INSTALL = "install"


    fun getNetworkHeader(context: Context): String {
        val userId = getUserId()
        if (userId.isBlank()) return ""

        val headerMap = hashMapOf<String,Any>().apply {
            put(HEADER_PACKAGE_ID,"com.dayor.managemob")
            put(HEADER_AF_ID, RemotePreferencesManager.instance.getRemoteMmpId())
            put(HEADER_APP_INSTANCE_ID, RemotePreferencesManager.instance.getRemoteInstanceId())
            put(HEADER_USER_ID, getUserId())
            put(HEADER_LANGUAGE, getLocalLanguage())
            put(HEADER_VERSION_NAME, getVersionName(context) ?: "")
            put(HEADER_VERSION_CODE, getVersionCode(context))
            put(HEADER_TIMEZONE, getTimeZoneOffset())
            put("timestamp", System.currentTimeMillis())
            put("operationTypeCate", getDefaultUserAgent(context) ?: "")
        }
        return EncodeUtil.encrypt(EncodeUtil.BASE_KEY, Gson().toJson(headerMap).toString())
    }


    private fun getVersionName(context: Context): String? {
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            return packageInfo.versionName
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun getVersionCode(context: Context): Int {
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) packageInfo.longVersionCode.toInt()
            else packageInfo.versionCode
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }

    fun getLocalLanguage(): String {
        return Locale.getDefault().toLanguageTag()
    }

    private suspend fun getAdvertisingId(): String {
        try {
            val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(CleanApp.appContext)
            val advertisingId = adInfo.id
            if (advertisingId != null) {
                return advertisingId
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }


    @OptIn(DelicateCoroutinesApi::class)
    fun initializeUserId() {
        if (getUserId().isNotBlank()) return
        GlobalScope.launch {
            val advertisingId = getAdvertisingId()
            val mmpId = RemotePreferencesManager.instance.getRemoteMmpId()
            var sanitizedId = ""
            advertisingId.forEach { if (it.isLetterOrDigit()) sanitizedId += it }
            if (advertisingId.isNotBlank() && !sanitizedId.matches(Regex("^0+$"))) {
                RemotePreferencesManager.instance.setRemoteMmpId(sanitizedId)
            } else if (mmpId.isNotBlank()) {
                RemotePreferencesManager.instance.setRemoteMmpId(mmpId)
            } else {
                val id = generateRandomUserId(16)
                RemotePreferencesManager.instance.setRemoteMmpId(id)
            }
        }
    }

    fun getUserId(): String {
        return RemotePreferencesManager.instance.getRemoteUserId()
    }

    fun getDefaultUserAgent(context: Context?): String? {
        return WebSettings.getDefaultUserAgent(context)
    }


    private fun generateRandomUserId(size: Int): String {
        val characters = "abcdefghijklmnopqrstuvwxyz0123456789"
        val random = Random(System.currentTimeMillis())

        val sb = StringBuilder(size)
        repeat(size) {
            val randomIndex = random.nextInt(characters.length)
            val randomChar = characters[randomIndex]
            sb.append(randomChar)
        }

        return sb.toString()
    }

    fun getTimeZoneOffset(): Int {
        val timeZone = TimeZone.getDefault()
        val offsetInMillis = timeZone.getOffset(System.currentTimeMillis())
        val offsetInMinutes = offsetInMillis / (60 * 1000)
        val offsetInHours = offsetInMinutes / 60
        return offsetInHours
    }
} 