package com.ext.remoteset.utils

import com.tencent.mmkv.MMKV

class RemotePreferencesManager private constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_CONFIG_PARAM = "rm_config_param"
        private const val KEY_CONFIG_TIME = "rm_config_time"
        private const val KEY_USER_ID = "rm_user_id"
        private const val KEY_MMP_ID = "rm_mmp_id"
        private const val KEY_INSTANCE_ID = "rm_instance_id"
        private const val KEY_INSTALL_FLAG = "rm_install_flag"
        private const val KEY_TOKEN_FLAG = "rm_token_flag"
        private const val KEY_GET_TOKEN = "rm_get_token"
        private const val KEY_TOKEN_TIME = "rm_token_time"
        private const val KEY_FB_FLASH_TIME = "rm_fb_flash_time"

        
        @Volatile
        private var INSTANCE: RemotePreferencesManager? = null

        val instance
            get() = INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemotePreferencesManager().also { INSTANCE = it }
            }
    }

    fun setRemoteConfigParams(params: String) {
        mmkv.putString(KEY_CONFIG_PARAM, params)
    }

    fun getRemoteConfigParams(): String {
        return mmkv.getString(KEY_CONFIG_PARAM, "") ?: ""
    }

    fun setRemoteConfigTime(time: Long) {
        mmkv.putLong(KEY_CONFIG_TIME, time)
    }

    fun getRemoteConfigTime(): Long {
        return mmkv.getLong(KEY_CONFIG_TIME, 0)
    }

    fun setRemoteUserId(id: String) {
        mmkv.putString(KEY_USER_ID, id)
    }

    fun getRemoteUserId(): String {
        return mmkv.getString(KEY_USER_ID, "") ?: ""
    }

    fun setRemoteMmpId(id: String) {
        mmkv.putString(KEY_MMP_ID, id)
    }

    fun getRemoteMmpId(): String {
        return mmkv.getString(KEY_MMP_ID, "") ?: ""
    }

    fun setRemoteInstanceId(id: String) {
        mmkv.putString(KEY_INSTANCE_ID, id)
    }

    fun getRemoteInstanceId(): String {
        return mmkv.getString(KEY_INSTANCE_ID, "") ?: ""
    }

    fun setRemoteInstallFlag(flag: Boolean) {
        mmkv.putBoolean(KEY_INSTALL_FLAG, flag)
    }

    fun getRemoteInstallFlag(): Boolean {
        return mmkv.getBoolean(KEY_INSTALL_FLAG, false)
    }

    fun setRemoteTokenFlag(flag: Boolean) {
        mmkv.putBoolean(KEY_TOKEN_FLAG, flag)
    }

    fun getRemoteTokenFlag(): Boolean {
        return mmkv.getBoolean(KEY_TOKEN_FLAG, false)
    }

    fun setRemoteGetToken(token: String) {
        mmkv.putString(KEY_GET_TOKEN, token)
    }

    fun getRemoteGetToken(): String {
        return mmkv.getString(KEY_GET_TOKEN, "") ?: ""
    }

    fun setRemoteTokenTime(time: Long) {
        mmkv.putLong(KEY_TOKEN_TIME, time)
    }

    fun getRemoteTokenTime(): Long {
        return mmkv.getLong(KEY_TOKEN_TIME, 0)
    }

    fun setRemoteFbFlashTime(time: Long) {
        mmkv.putLong(KEY_FB_FLASH_TIME, time)
    }

    fun getRemoteFbFlashTime(): Long {
        return mmkv.getLong(KEY_FB_FLASH_TIME, 0)
    }
} 