package com.ext.remoteset.utils

import android.util.Log
import com.example.clean0522.BuildConfig
import com.ext.firbase.UpdateEvent

object PostLogger {
    var enableLogging = BuildConfig.DEBUG

    fun Any.logPost(message: String) {
        if (enableLogging) {
            Log.d(this::class.java.simpleName, message)
        }
    }

    fun Any.logPost(tag: String, message: String) {
        if (enableLogging) {
            Log.d(tag, message)
        }
    }

    fun trackAnalyticsEvent(event: String) {
        UpdateEvent.event(event)
    }
} 