package com.ext.remoteset

import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

interface AnalyticsApiService {
    @POST("expressRest")
    fun sendEventData(@Body requestBody: RequestBody): Call<ResponseBody>

    @POST("spareClass")
    fun sendAdValueData(@Body requestBody: RequestBody): Call<ResponseBody>

    @POST("hardLoop")
    fun sendCustomEvent(@Body requestBody: RequestBody): Call<ResponseBody>

    @POST("signFile")
    fun fetchConfiguration(@Body requestBody: RequestBody): Call<ResponseBody>

    @POST("fullView")
    fun updateDeviceToken(@Body requestBody: RequestBody): Call<ResponseBody>
} 