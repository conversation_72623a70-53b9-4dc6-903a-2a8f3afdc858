package com.ext.remoteset.data

import androidx.room.*

@Dao
interface CreateEventDao {

    @Query("SELECT * FROM createEvent")
    fun getEvents(): MutableList<CreateEventData>

    @Query("DELETE FROM createEvent")
    fun deleteTab()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertEvents(data: CreateEventData):Long

    @Update
    fun updateEvents(data: CreateEventData):Int

    @Delete
    fun deleteEvent(data: CreateEventData):Int
}