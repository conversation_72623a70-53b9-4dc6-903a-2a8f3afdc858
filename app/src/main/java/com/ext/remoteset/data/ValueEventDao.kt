package com.ext.remoteset.data

import androidx.room.*

@Dao
interface ValueEventDao {

    @Query("SELECT * FROM valueEvent")
    fun getEvents(): MutableList<ValueEventData>

    @Query("DELETE FROM valueEvent")
    fun deleteTab()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertEvents(data: ValueEventData):Long

    @Update
    fun updateEvents(data: ValueEventData):Int

    @Delete
    fun deleteEvent(data: ValueEventData):Int
}