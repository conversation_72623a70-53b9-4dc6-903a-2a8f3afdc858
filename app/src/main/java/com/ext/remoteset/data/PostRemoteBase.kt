package com.ext.remoteset.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

private const val DATABASE_VERSION = 1
private const val TABLE_NAME = "bi_table.db"

@Database(
    entities = [StartEventData::class, ValueEventData::class, CreateEventData::class],
    version = DATABASE_VERSION,
    exportSchema = false
)
abstract class PostRemoteBase : RoomDatabase() {
    abstract fun startEventDao(): StartEventDao
    abstract fun valueEventDao(): ValueEventDao
    abstract fun customEventDao(): CreateEventDao

    companion object{
        private var instance: PostRemoteBase? = null
        fun getInstance(context: Context): PostRemoteBase {
            if(instance == null){
                synchronized(PostRemoteBase::class){
                    instance = Room.databaseBuilder(context, PostRemoteBase::class.java, TABLE_NAME)
                        .allowMainThreadQueries()
                        .build()
                }
            }
            return instance !!
        }
    }
}