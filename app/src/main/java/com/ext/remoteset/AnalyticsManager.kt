package com.ext.remoteset

import android.app.Activity
import androidx.collection.ArraySet
import com.applovin.mediation.MaxAd
import com.example.clean0522.CleanApp
import com.ext.remoteset.data.PostRemoteBase
import com.ext.remoteset.data.CreateEventData
import com.ext.remoteset.data.StartEventData
import com.ext.remoteset.data.ValueEventData
import com.ext.remoteset.utils.EncodeUtil
import com.ext.remoteset.utils.PostLogger
import com.ext.remoteset.utils.PostLogger.logPost
import com.ext.remoteset.utils.RemotePreferencesManager
import com.google.android.gms.ads.AdValue
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException

object AnalyticsManager {

    private val TAG = "AnalyticsManager"

    private fun getApiService(): AnalyticsApiService {
        val retrofit = Retrofit.Builder()
            .baseUrl(AnalyticsConstants.API_BASE_URL)
            .addConverterFactory(GsonConverterFactory.create(Gson().newBuilder().create()))
            .client(OkHttpClient.Builder().addInterceptor(RequestInterceptor()).build())
            .build()

        return retrofit.create(AnalyticsApiService::class.java)
    }

    fun sendAppEvent(eventName: String, activity: Activity?) {
        val bodyMap = LinkedHashMap<String, Any>()
        val type = if (activity.toString().startsWith(CleanApp.appContext.packageName)) 1 else 2
        bodyMap["mask"] = eventName
        bodyMap["which"] = if (eventName == AnalyticsConstants.EVENT_TYPE_INSTALL) "" else activity.toString()
        bodyMap["type"] = type
        val requestBody = createRequestBody(bodyMap)

        if (requestBody != null) {
            getApiService().sendEventData(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        if (eventName == AnalyticsConstants.EVENT_TYPE_INSTALL) {
                            RemotePreferencesManager.instance.setRemoteInstallFlag(true)
                        }
                    } else {
                        if (eventName == AnalyticsConstants.EVENT_TYPE_START) {
                            PostRemoteBase.getInstance(CleanApp.appContext).startEventDao().insertEvents(
                                StartEventData(data = bodyMap)
                            )
                        }
                    }
                    logPost(TAG, "sendAppEvent data: ${bodyMap}")
                    logPost(TAG, "sendAppEvent success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    if (eventName == AnalyticsConstants.EVENT_TYPE_START) {
                        PostRemoteBase.getInstance(CleanApp.appContext).startEventDao().insertEvents(
                            StartEventData(data = bodyMap)
                        )
                    }
                    logPost(TAG, "sendAppEvent fail: ${t.message}")
                }
            })
        }
    }

    fun sendEventBatch(events: MutableList<HashMap<String, Any>>) {
        if (events.isEmpty()) return
        val requestBody = createBatchRequestBody(events)
        if (requestBody != null) {
            getApiService().sendEventData(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        GlobalScope.launch { PostRemoteBase.getInstance(CleanApp.appContext).startEventDao().deleteTab() }
                    }
                    logPost(TAG, "sendEventBatch success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendEventBatch fail: ${t.message}")
                }
            })
        }
    }

    fun sendMaxAdRevenue(maxAd: MaxAd, adPlace: String = "") {
        val value = maxAd.revenue
        if (value == 0.0) return
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["value"] = value
        bodyMap["cny"] = "USD"
        bodyMap["suit"] = maxAd.revenuePrecision ?: 0
        bodyMap["from"] = maxAd.networkName ?: ""
        bodyMap["type"] = maxAd.format.displayName ?: ""
        bodyMap["placement"] = adPlace

        val requestBody = createRequestBody(bodyMap)
        if (requestBody != null) {
            getApiService().sendAdValueData(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        logPost(TAG, "sendMaxAdRevenue success: $response")
                    } else {
                        logPost(TAG, "sendMaxAdRevenue no success: $response")
                        PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().insertEvents(
                            ValueEventData(data = bodyMap)
                        )
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendMaxAdRevenue error: ${t.message}")
                    PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().insertEvents(ValueEventData(data = bodyMap))
                }
            })
        }
    }

    fun sendAdmobRevenue(adValue: AdValue?, adSource: String?, format: String?, adPlace: String = "") {
        if (adValue == null) return
        val value = (adValue.valueMicros.toDouble() / 1000000)
        if (value == 0.0) return
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["value"] = value
        bodyMap["cny"] = "USD"
        bodyMap["suit"] = adValue.precisionType
        bodyMap["from"] = adSource ?: ""
        bodyMap["type"] = format ?: ""
        bodyMap["placement"] = adPlace

        val requestBody = createRequestBody(bodyMap)
        if (requestBody != null) {
            getApiService().sendAdValueData(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        logPost(TAG, "sendAdmobRevenue success: $response")
                    } else {
                        logPost(TAG, "sendAdmobRevenue no success: $response")
                        PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().insertEvents(
                            ValueEventData(data = bodyMap)
                        )
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendAdmobRevenue error: ${t.message}")
                    PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().insertEvents(ValueEventData(data = bodyMap))
                }
            })
        }
    }

    fun sendAdValueBatch(values: MutableList<HashMap<String, Any>>) {
        if (values.isEmpty()) return
        val requestBody = createBatchRequestBody(values)
        if (requestBody != null) {
            getApiService().sendAdValueData(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        GlobalScope.launch { PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().deleteTab() }
                    }
                    logPost(TAG, "sendAdValueBatch success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendAdValueBatch fail: ${t.message}")
                }
            })
        }
    }

    fun sendCustomEvent(key: String, params: ArraySet<String>) {
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["event"] = key
        bodyMap["loops"] = params
        val requestBody = createRequestBody(bodyMap)
        if (requestBody != null) {
            getApiService().sendCustomEvent(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    logPost(TAG, "sendCustomEvent success: ${response}")
                    if (response.isSuccessful) {
                        logPost(TAG, "sendCustomEvent success: $response")
                    } else {
                        PostRemoteBase.getInstance(CleanApp.appContext).customEventDao().insertEvents(
                            CreateEventData(data = bodyMap)
                        )
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendCustomEvent fail: ${t.message}")
                    PostRemoteBase.getInstance(CleanApp.appContext).customEventDao().insertEvents(
                        CreateEventData(data = bodyMap)
                    )
                }
            })
        }
    }

    fun sendTipPost(data: String) {
        sendCustomEvent("tip_post_event", ArraySet<String>().apply { add(data) })
    }

    fun sendTipClick(data: String) {
        sendCustomEvent("tip_click_event", ArraySet<String>().apply { add(data) })
    }

    fun sendCustomEventBatch(events: MutableList<HashMap<String, Any>>) {
        if (events.isEmpty()) return
        val requestBody = createBatchRequestBody(events)
        if (requestBody != null) {
            getApiService().sendCustomEvent(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        GlobalScope.launch { PostRemoteBase.getInstance(CleanApp.appContext).customEventDao().deleteTab() }
                    }
                    logPost(TAG, "sendCustomEventBatch success: ${response}")
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "sendCustomEventBatch fail: ${t.message}")
                }
            })
        }
    }

    fun updateToken(token: String) {
        if (token.isBlank()) return
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["fcm_token"] = token
        val requestBody = createRequestBody(bodyMap)
        if (requestBody != null) {
            getApiService().updateDeviceToken(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        logPost(TAG, "updateToken success: $response")
                        RemotePreferencesManager.instance.setRemoteTokenFlag(true)
                        PostLogger.trackAnalyticsEvent("post_token_suc")
                    } else {
                        logPost(TAG, "updateToken no success: $response")
                        PostLogger.trackAnalyticsEvent("post_token_false")
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    logPost(TAG, "updateToken error: ${t.message}")
                    PostLogger.trackAnalyticsEvent("post_token_false")
                }
            })
        }
    }

    fun fetchConfiguration(success: (json: String?) -> Unit, fail: () -> Unit) {
        val bodyMap = LinkedHashMap<String, Any>()
        bodyMap["lang"] = AnalyticsConstants.getLocalLanguage()
        bodyMap["bcode"] = AnalyticsConstants.getVersionCode(CleanApp.appContext)
        val requestBody = createRequestBody(bodyMap)

        logPost(TAG, "request fetchConfiguration: $requestBody")
        PostLogger.trackAnalyticsEvent("request_remote_config")
        if (requestBody != null) {
            getApiService().fetchConfiguration(requestBody).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    if (response.isSuccessful) {
                        logPost(TAG, "fetchConfiguration success")
                        success.invoke(response.body()?.string())
                    } else {
                        fail.invoke()
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    fail.invoke()
                    logPost(TAG, "fetchConfiguration failed")
                }
            })
        } else {
            fail.invoke()
        }
    }

    private fun createRequestBody(bodyMap: HashMap<String, Any>): RequestBody? {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val json = gson.toJson(bodyMap)
        val finalJson = EncodeUtil.encrypt(EncodeUtil.BASE_KEY, json)
        val jsonObject = JSONObject().apply {
            put("rewards", finalJson)
        }

        return try {
            RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        } catch (e: Exception) {
            null
        }
    }

    private fun createBatchRequestBody(list: MutableList<HashMap<String, Any>>): RequestBody? {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val json = gson.toJson(list)
        val finalJson = EncodeUtil.encrypt(EncodeUtil.BASE_KEY, json)
        val jsonObject = JSONObject().apply {
            put("rewards", finalJson)
        }
        return try {
            RequestBody.create("application/json".toMediaTypeOrNull(), jsonObject.toString())
        } catch (e: Exception) {
            null
        }
    }

    fun checkAndRequestErrorPost(){
        GlobalScope.launch {
            val expressEvents = PostRemoteBase.getInstance(CleanApp.appContext).startEventDao().getEvents()
            if (expressEvents.isNotEmpty()){
                val localList = mutableListOf<HashMap<String,Any>>()
                expressEvents.forEach {
                    localList.add(it.data)
                }
                sendEventBatch(localList)
            }

            val spareEvents = PostRemoteBase.getInstance(CleanApp.appContext).valueEventDao().getEvents()
            if (spareEvents.isNotEmpty()){
                val localList = mutableListOf<HashMap<String,Any>>()
                spareEvents.forEach {
                    localList.add(it.data)
                }
                sendAdValueBatch(localList)
            }

            val hardLoopEvents = PostRemoteBase.getInstance(CleanApp.appContext).customEventDao().getEvents()
            if (hardLoopEvents.isNotEmpty()){
                val localList = mutableListOf<HashMap<String,Any>>()
                hardLoopEvents.forEach {
                    localList.add(it.data)
                }
                sendCustomEventBatch(localList)
            }

            RemotePreferencesManager.instance.apply {
                if (!getRemoteTokenFlag() && getRemoteGetToken().isNotBlank()){
                    updateToken(RemotePreferencesManager.instance.getRemoteGetToken())
                }
            }
        }
    }

    class RequestInterceptor : Interceptor {
        @Throws(IOException::class)
        override fun intercept(chain: Interceptor.Chain): okhttp3.Response {
            val request = chain.request().newBuilder()
                .addHeader("api-version", "1")
                .addHeader("reward-no", "com.dayor.managemob")
                .addHeader("reward-lift", AnalyticsConstants.getNetworkHeader(CleanApp.appContext))
                .build()
            return chain.proceed(request)
        }
    }
} 