package com.ext.firbase

import android.os.Bundle
import com.applovin.mediation.MaxAd
import com.example.clean0522.BuildConfig
import com.ext.adfusion.util.AdFusionPreferences
import com.google.android.gms.ads.AdValue
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase

object UpdateEvent {
    private var analyticsInstance: FirebaseAnalytics? = null

    private fun getInstance(): FirebaseAnalytics?{
        if (analyticsInstance == null){
            analyticsInstance = Firebase.analytics
        }
        return analyticsInstance
    }

    fun updateMaxAdRevenue(ad: MaxAd?) {
        if (BuildConfig.DEBUG || ad == null) return
        val bundle = Bundle()
        bundle.putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
        bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD") //The revenue
        bundle.putString("bdNetwork", ad.networkName)
        bundle.putString("bdFormat", ad.format.label)
        event("revenue_by_impress", bundle)
    }

    fun updateAdmobAdRevenue(adValue: AdValue?, adSource: String?, format: String?) {
        if (BuildConfig.DEBUG || adValue == null) return
        val bundle = Bundle()
        val revenue = adValue.valueMicros.toDouble() / 1000000
        bundle.putDouble(FirebaseAnalytics.Param.VALUE, revenue)
        bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD") //The revenue
        bundle.putString("bdNetwork", adSource)
        bundle.putString("bdFormat", format)
        event("revenue_by_impress", bundle)
    }

    fun updateTotalRevenue(adValue: Double?){
        if (BuildConfig.DEBUG || adValue == null) return
        val currentImpressionRevenue = adValue
        val lastTotalValue = AdFusionPreferences.instance.getAdValueTotal()
        val currentValue = currentImpressionRevenue + lastTotalValue
        if (currentValue >= 0.01){
            val bundle = Bundle()
            bundle.putDouble(FirebaseAnalytics.Param.VALUE, currentValue)
            bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            event("Total_001_by_revenue",bundle)
            AdFusionPreferences.instance.setAdValueTotal(0f)
        }else{
            AdFusionPreferences.instance.setAdValueTotal(currentValue.toFloat())
        }
    }

    fun maxAdImpression(maxAd: MaxAd?) {
        if (BuildConfig.DEBUG) return
        val bundle = Bundle()
        maxAd?.let {bundle.putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
            bundle.putString(FirebaseAnalytics.Param.AD_UNIT_NAME, maxAd.adUnitId)
            bundle.putString(FirebaseAnalytics.Param.AD_FORMAT, maxAd.format.label)
            bundle.putString(FirebaseAnalytics.Param.AD_SOURCE, maxAd.networkName)
            bundle.putDouble(FirebaseAnalytics.Param.VALUE, maxAd.revenue)
            bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD")
        }
        event(FirebaseAnalytics.Event.AD_IMPRESSION,bundle)
    }

    fun admobAdImpression(adValue: AdValue?, adSource: String?, format: String?) {
        if (BuildConfig.DEBUG) return
        val bundle = Bundle()
        if (adValue != null) {
            bundle.putString(FirebaseAnalytics.Param.AD_PLATFORM, "admob")
            bundle.putString(FirebaseAnalytics.Param.AD_FORMAT, format)
            bundle.putString(FirebaseAnalytics.Param.AD_SOURCE, adSource)
            bundle.putDouble(FirebaseAnalytics.Param.VALUE, adValue.valueMicros.toDouble() / 1000000)
            bundle.putString(FirebaseAnalytics.Param.CURRENCY, "USD")
        }
        event(FirebaseAnalytics.Event.AD_IMPRESSION, bundle)
    }


    fun event(key: String, bundle: Bundle = Bundle()){
        if (BuildConfig.DEBUG) return
        getInstance()?.logEvent(key, bundle)
    }
}