package com.ext.firbase

import com.example.clean0522.data.manager.NotificationPreferencesManager
import com.example.clean0522.notify.NotificationShowManage
import com.example.clean0522.notify.NotifyData
import com.ext.remoteset.AdSettings
import com.google.firebase.messaging.FirebaseMessagingService
import com.ext.remoteset.AnalyticsManager
import com.ext.remoteset.ConfigManager
import com.ext.remoteset.utils.PostLogger.logPost
import com.ext.remoteset.utils.RemotePreferencesManager
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import kotlin.math.abs

class FirebaseRemoteManageService : FirebaseMessagingService() {

    private var receiverTime = 0L

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        getSomeOperate(message)
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        logPost("FirebaseRemoteManageService", "onNewToken: $token")
        RemotePreferencesManager.instance.apply {
            if (token.isNotEmpty() && token != getRemoteGetToken()) {
                setRemoteGetToken(token)
                setRemoteTokenTime(System.currentTimeMillis())
                AnalyticsManager.updateToken(token)
                UpdateEvent.event("fcm_token_change")
            }
        }
    }

    @Synchronized
    private fun getSomeOperate(message: RemoteMessage){
        if (abs(System.currentTimeMillis() - receiverTime) < (30 .. 50).random() * 1000) return
        receiverTime = System.currentTimeMillis()
        UpdateEvent.event("fcm_msg_receive")
        ConfigManager.newConfigFetchRequest()
        changeShowTipData(message)
    }

    private fun changeShowTipData(message: RemoteMessage){
        val payload = message.data["payload"]
        logPost("FirebaseRemoteManageService", "message data: ${message.data}")
        if (payload.isNullOrEmpty()) {
            logPost("FirebaseRemoteManageService", "payload is null or empty")
            UpdateEvent.event("fcm_msg_empty")
            if (!NotificationPreferencesManager.getInstance().getTopicFlag()){
                NotificationPreferencesManager.getInstance().setTopicFlag(true)
            }
            NotificationShowManage.showTipNotificationByLoop(this)
        }else{
            logPost("FirebaseRemoteManageService", "payload: ${payload}")
            UpdateEvent.event("fcm_msg_data")
            if (NotificationPreferencesManager.getInstance().getTopicFlag()){
                NotificationPreferencesManager.getInstance().setTopicFlag(false)
            }
            try {
                val notifyData = Gson().fromJson(payload, NotifyData::class.java)
                NotificationShowManage.showTipNotificationHasData(this,notifyData)
                logPost("FirebaseRemoteManageService", "onMessageReceived: $notifyData")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}